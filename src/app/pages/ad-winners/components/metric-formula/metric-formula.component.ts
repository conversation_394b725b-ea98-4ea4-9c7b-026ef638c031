import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressBarModule } from 'primeng/progressbar';
import { PerformanceScoreConfiguration, PerformanceScoreMetric } from '../../models';

interface MetricWeight {
  metric: string;
  label: string;
  weight: number;
  isPrimary: boolean;
  description: string;
  format: string;
}

@Component({
  selector: 'chm-metric-formula',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    TagModule,
    TooltipModule,
    ProgressBarModule,
  ],
  template: `
    <div class="metric-formula-container">
      <p-card styleClass="formula-card">
        <div class="compact-content">
          <div class="header-section">
            <div class="title-row">
              <i class="pi pi-chart-pie title-icon"></i>
              <span class="title-text">Performance Score</span>
              <p-tag
                [value]="getPrimaryMetricName()"
                severity="success"
                styleClass="primary-tag"
              >
              </p-tag>
            </div>
            <div class="subtitle">{{ getConfigurationName() }}</div>
          </div>

          <div class="metrics-grid">
            <div
              *ngFor="let metricWeight of getMetricWeights()"
              class="metric-item"
              [class.primary]="metricWeight.isPrimary"
            >
              <div class="metric-row">
                <span class="metric-label">{{ metricWeight.label }}</span>
                <span class="metric-weight"
                  >{{ (metricWeight.weight * 100).toFixed(1) }}%</span
                >
              </div>
              <div class="metric-bar">
                <div
                  class="bar-fill"
                  [class.primary-bar]="metricWeight.isPrimary"
                  [style.width.%]="metricWeight.weight * 100"
                ></div>
              </div>
            </div>
          </div>

          <div class="formula-summary">
            <i class="pi pi-calculator"></i>
            <span>Score = Σ (Metric × Weight)</span>
          </div>
        </div>
      </p-card>
    </div>
  `,
  styleUrls: ['./metric-formula.component.css'],
})
export class MetricFormulaComponent {
  @Input() configuration: PerformanceScoreConfiguration | null = null;

  getMetricWeights(): MetricWeight[] {
    if (!this.configuration) {
      // Default E-commerce configuration
      return [
        { metric: 'roas', label: 'ROAS', weight: 0.35, isPrimary: true, description: 'Return on Ad Spend', format: 'number' },
        { metric: 'conversion_rate', label: 'Conversion Rate', weight: 0.30, isPrimary: false, description: 'Purchase rate', format: 'percentage' },
        { metric: 'traffic_quality', label: 'Traffic Quality', weight: 0.15, isPrimary: false, description: 'Landing page view rate', format: 'percentage' },
        { metric: 'cpa', label: 'CPA', weight: 0.10, isPrimary: false, description: 'Cost per acquisition', format: 'currency' },
        { metric: 'hook_rate', label: 'Hook Rate', weight: 0.05, isPrimary: false, description: 'Video view rate', format: 'percentage' },
        { metric: 'hold_rate', label: 'Hold Rate', weight: 0.05, isPrimary: false, description: 'Video retention rate', format: 'percentage' },
      ];
    }

    // Get active metrics from configuration
    const activeMetrics = this.configuration.metrics_config.metrics.filter(m => m.weight > 0);
    const primaryMetric = activeMetrics.sort((a, b) => b.weight - a.weight)[0];

    return activeMetrics.map(metric => ({
      metric: metric.metric,
      label: metric.label,
      weight: metric.weight / 100, // Convert percentage to decimal
      isPrimary: metric === primaryMetric,
      description: metric.description,
      format: metric.format,
    })).sort((a, b) => b.weight - a.weight);
  }

  getPrimaryMetricName(): string {
    const weights = this.getMetricWeights();
    const primary = weights.find(w => w.isPrimary);
    return primary?.label || 'ROAS';
  }

  getConfigurationName(): string {
    return this.configuration?.name || 'Default E-commerce';
  }
}
