import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'chm-ad-media',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ad-media-container">
      <!-- Video -->
      <video 
        *ngIf="videoUrl && !imageError"
        [src]="videoUrl"
        class="ad-media-video"
        [muted]="true"
        [loop]="true"
        [autoplay]="false"
        [controls]="false"
        (error)="onVideoError()"
        (loadeddata)="onVideoLoaded()">
      </video>
      
      <!-- Image fallback -->
      <img 
        *ngIf="(!videoUrl || imageError) && imageUrl"
        [src]="imageUrl"
        [alt]="altText"
        class="ad-media-image"
        (error)="onImageError()"
        (load)="onImageLoaded()">
      
      <!-- Placeholder -->
      <div 
        *ngIf="(!videoUrl && !imageUrl) || (imageError && !imageUrl)"
        class="ad-media-placeholder">
        <i class="pi pi-image"></i>
        <span>No media available</span>
      </div>
      
      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="ad-media-loading">
        <i class="pi pi-spin pi-spinner"></i>
      </div>
    </div>
  `,
  styles: [`
    .ad-media-container {
      position: relative;
      width: 100%;
      height: 200px;
      border-radius: 0.5rem;
      overflow: hidden;
      background: #f8fafc;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ad-media-video,
    .ad-media-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.5rem;
    }

    .ad-media-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      color: #64748b;
      font-size: 0.875rem;
    }

    .ad-media-placeholder i {
      font-size: 2rem;
      color: #cbd5e1;
    }

    .ad-media-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #5521be;
      font-size: 1.5rem;
    }

    /* Hover effects */
    .ad-media-container:hover .ad-media-video {
      transform: scale(1.02);
      transition: transform 0.3s ease;
    }

    .ad-media-container:hover .ad-media-image {
      transform: scale(1.02);
      transition: transform 0.3s ease;
    }
  `]
})
export class AdMediaComponent {
  @Input() videoUrl: string | null = null;
  @Input() imageUrl: string | null = null;
  @Input() altText: string = 'Ad media';
  @Input() autoplay: boolean = false;

  imageError = false;
  videoError = false;
  isLoading = false;

  onVideoError(): void {
    this.videoError = true;
    this.isLoading = false;
  }

  onVideoLoaded(): void {
    this.isLoading = false;
    if (this.autoplay) {
      // Auto play video on hover or when loaded
      const video = document.querySelector('.ad-media-video') as HTMLVideoElement;
      if (video) {
        video.play().catch(() => {
          // Ignore autoplay errors
        });
      }
    }
  }

  onImageError(): void {
    this.imageError = true;
    this.isLoading = false;
  }

  onImageLoaded(): void {
    this.isLoading = false;
  }
}
