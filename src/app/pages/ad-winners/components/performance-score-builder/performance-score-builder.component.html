<p-dialog
  (onHide)="closeDialog()"
  [(visible)]="visible"
  [closable]="true"
  [draggable]="false"
  [maximizable]="false"
  [modal]="true"
  [resizable]="false"
  [style]="{ width: '100vw', height: '100vh', maxWidth: 'none', maxHeight: 'none' }"
  styleClass="fullscreen-score-builder">

  <ng-template pTemplate="header">
    <div class="fullscreen-header">
      <div class="header-icon">
        <i class="pi pi-chart-line"></i>
      </div>
      <div class="header-title">
        <h2>Performance Score Builder</h2>
        <p>Create your perfect performance scoring formula</p>
      </div>
    </div>
  </ng-template>

  <div class="fullscreen-content">
    <!-- Top Section: Templates -->
    <div class="templates-section">
      <!-- Predefined Templates -->
      <div class="template-subsection">
        <h3>
          <i class="pi pi-bookmark"></i>
          Predefined Templates
        </h3>
        <div class="template-cards">
          <div
            *ngFor="let preset of systemConfigurations"
            class="template-card"
            [ngClass]="getTemplateCardClass(preset)"
            (click)="loadPreset(preset.id!)">
            <div class="template-header">
              <h4>{{ preset.name }}</h4>
              <p-tag
                *ngIf="preset.is_default"
                value="Recommended"
                severity="success"
                size="small">
              </p-tag>
            </div>
            <p class="template-description">{{ preset.description }}</p>
            <div class="template-preview">
              <div
                *ngFor="let metric of getPresetPreviewMetrics(preset)"
                class="metric-preview"
                [style.background-color]="metric.color + '20'"
                [style.border-color]="metric.color">
                <span class="metric-name">{{ metric.label }}</span>
                <span class="metric-weight">{{ metric.weight }}%</span>
              </div>
              <span *ngIf="hasMoreMetrics(preset)" class="more-metrics">
                +{{ getPresetMoreMetricsCount(preset) }} more
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Custom Templates -->
      <div class="template-subsection" *ngIf="customConfigurations.length > 0">
        <div class="subsection-header">
          <h3>
            <i class="pi pi-cog"></i>
            Custom Templates
          </h3>
          <p-button
            label="Clear All"
            icon="pi pi-trash"
            severity="danger"
            size="small"
            [text]="true"
            (onClick)="clearAllCustomTemplates()">
          </p-button>
        </div>
        <div class="template-cards">
          <div
            *ngFor="let custom of customConfigurations"
            class="template-card custom-template"
            [ngClass]="getTemplateCardClass(custom)"
            (click)="loadPreset(custom.id!)">
            <div class="template-header">
              <h4>{{ custom.name }}</h4>
              <div class="custom-actions">
                <p-button
                  icon="pi pi-pencil"
                  severity="secondary"
                  size="small"
                  [text]="true"
                  pTooltip="Edit"
                  (onClick)="editCustomTemplate($event, custom)">
                </p-button>
                <p-button
                  icon="pi pi-trash"
                  severity="danger"
                  size="small"
                  [text]="true"
                  pTooltip="Delete"
                  (onClick)="deleteCustomTemplate($event, custom)">
                </p-button>
              </div>
            </div>
            <p class="template-description">{{ custom.description }}</p>
            <div class="template-preview">
              <div
                *ngFor="let metric of getPresetPreviewMetrics(custom)"
                class="metric-preview"
                [style.background-color]="metric.color + '20'"
                [style.border-color]="metric.color">
                <span class="metric-name">{{ metric.label }}</span>
                <span class="metric-weight">{{ metric.weight }}%</span>
              </div>
              <span *ngIf="hasMoreMetrics(custom)" class="more-metrics">
                +{{ getPresetMoreMetricsCount(custom) }} more
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-grid">
      <!-- Left: Custom Templates -->
      <div class="custom-section">
        <div class="section-header">
          <h3>
            <i class="pi pi-cog"></i>
            Custom Formula
          </h3>
          <div class="weight-indicator" [ngClass]="getValidationStatusClass()">
            <span class="weight-value">{{ totalWeight }}%</span>
            <span class="weight-label">Total Weight</span>
          </div>
        </div>

        <!-- Template Creation Form -->
        <div class="template-creation-form">
          <div class="form-row">
            <div class="form-field">
              <label for="templateName">Template Name</label>
              <p-inputText
                [(ngModel)]="newTemplateName"
                id="templateName"
                placeholder="Enter template name"
                styleClass="w-full">
              </p-inputText>
            </div>
            <div class="form-field">
              <label for="templateDescription">Description</label>
              <p-inputText
                [(ngModel)]="newTemplateDescription"
                id="templateDescription"
                placeholder="Enter description (optional)"
                styleClass="w-full">
              </p-inputText>
            </div>
          </div>
          <div class="form-actions">
            <p-button
              (onClick)="createCustomTemplate()"
              [disabled]="!canCreateTemplate()"
              icon="pi pi-plus"
              label="Create Template"
              severity="success"
              size="small">
            </p-button>
            <p-button
              (onClick)="setAsDefault()"
              [disabled]="!isValidConfiguration"
              icon="pi pi-star"
              label="Set as Default"
              severity="warning"
              size="small"
              [outlined]="true">
            </p-button>
          </div>
        </div>

        <div class="metrics-grid">
          <div
            *ngFor="let metric of availableMetrics"
            class="metric-card"
            [ngClass]="getMetricCardClass(metric)">

            <div class="metric-info">
              <div class="metric-icon" [style.background-color]="metric.color">
                <i class="pi pi-chart-bar"></i>
              </div>
              <div class="metric-details">
                <h4>{{ metric.label }}</h4>
                <p>{{ metric.description }}</p>
              </div>
            </div>

            <div class="metric-control" *ngIf="isMetricActive(metric)">
              <div class="weight-display">
                <span class="weight-number">{{ metric.weight }}%</span>
              </div>
              <div class="weight-controls">
                <div class="weight-adjuster">
                  <button
                    (click)="decreaseWeight(metric)"
                    [disabled]="isDecreaseDisabled(metric)"
                    class="weight-btn weight-btn-minus"
                    type="button">
                    <i class="pi pi-minus"></i>
                  </button>
                  <div class="weight-input-container">
                    <p-inputNumber
                      [(ngModel)]="metric.weight"
                      (onInput)="onDirectWeightChange(metric, metric.weight)"
                      [min]="0"
                      [max]="100"
                      [step]="5"
                      [showButtons]="false"
                      size="small"
                      suffix="%"
                      styleClass="compact-weight-input">
                    </p-inputNumber>
                  </div>
                  <button
                    (click)="increaseWeight(metric)"
                    [disabled]="isIncreaseDisabled(metric)"
                    class="weight-btn weight-btn-plus"
                    type="button">
                    <i class="pi pi-plus"></i>
                  </button>
                </div>
                <button
                  (click)="removeMetric(metric)"
                  class="delete-btn"
                  type="button"
                  title="Remove metric">
                  <i class="pi pi-trash"></i>
                </button>
              </div>
            </div>

            <div class="metric-add" *ngIf="isMetricInactive(metric)">
              <button
                (click)="addMetric(metric)"
                class="add-metric-btn"
                type="button">
                <i class="pi pi-plus"></i>
                <span>Add to Formula</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right: Live Preview -->
      <div class="preview-section">
        <div class="section-header">
          <h3>
            <i class="pi pi-eye"></i>
            Live Preview
          </h3>
          <p class="preview-subtitle">See how your formula affects real ad scores</p>
        </div>

        <div class="preview-cards" *ngIf="hasPreviewData()">
          <div
            *ngFor="let preview of previewData"
            class="preview-card">
            <div class="preview-header">
              <div class="ad-info">
                <h4 class="ad-name">{{ preview.adName }}</h4>
                <span class="ad-type">{{ getAdType(preview) }}</span>
              </div>
              <div class="score-comparison">
                <div class="score-item old">
                  <span class="score-label">Current</span>
                  <span class="score-value">{{ preview.currentScore }}%</span>
                </div>
                <i class="pi pi-arrow-right score-arrow"></i>
                <div class="score-item new" [ngClass]="getScoreChangeClass(preview)">
                  <span class="score-label">New</span>
                  <span class="score-value">{{ preview.newScore }}%</span>
                </div>
              </div>
            </div>

            <div class="metrics-breakdown">
              <div
                *ngFor="let metric of getActiveMetricsForPreview()"
                class="breakdown-metric">
                <div class="metric-info">
                  <div class="metric-dot" [style.background-color]="metric.color"></div>
                  <span class="metric-name">{{ metric.label }}</span>
                  <span class="metric-weight">({{ metric.weight }}%)</span>
                </div>
                <span class="metric-value">{{ formatMetricValue(preview.metrics[metric.metric], metric.format) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="no-preview" *ngIf="!hasPreviewData()">
          <i class="pi pi-info-circle"></i>
          <p>No ad data available for preview</p>
        </div>
      </div>
    </div>

    <!-- Footer Actions -->
    <div class="fullscreen-footer">
      <div class="footer-left">
        <div class="validation-status" [ngClass]="getValidationStatusClass()">
          <i class="pi" [ngClass]="getValidationIconClass()"></i>
          <span>{{ getValidationMessage() }}</span>
        </div>
      </div>
      <div class="footer-actions">
        <p-button
          (onClick)="resetToDefault()"
          icon="pi pi-refresh"
          label="Reset"
          severity="secondary"
          [outlined]="true">
        </p-button>

        <p-button
          (onClick)="closeDialog()"
          icon="pi pi-times"
          label="Cancel"
          severity="secondary"
          [outlined]="true">
        </p-button>
        <p-button
          (onClick)="applyConfiguration()"
          [disabled]="!isValidConfiguration"
          icon="pi pi-check"
          label="Apply Formula"
          severity="success">
        </p-button>
      </div>
    </div>
  </div>


</p-dialog>
