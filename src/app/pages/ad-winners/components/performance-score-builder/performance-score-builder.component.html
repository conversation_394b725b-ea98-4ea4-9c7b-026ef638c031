<p-dialog
  (onHide)="closeDialog()"
  [(visible)]="visible"
  [closable]="true"
  [draggable]="false"
  [maximizable]="false"
  [modal]="true"
  [resizable]="false"
  [style]="{ width: '90vw', maxWidth: '900px', height: 'auto' }"
  header="Performance Score Builder"
  styleClass="score-builder-dialog">

  <div class="score-builder-container">
    <!-- Header Section -->
    <div class="builder-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <i class="pi pi-chart-line"></i>
          </div>
          <div class="header-text">
            <h2>Customize Performance Scoring</h2>
            <p>Build your perfect performance score formula by adjusting metric weights</p>
          </div>
        </div>
        <div class="header-right">
          <div [class.invalid]="!isValidConfiguration" [class.valid]="isValidConfiguration"
               class="total-weight-indicator">
            <span class="weight-label">Total Weight</span>
            <span class="weight-value">{{ totalWeight }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="builder-content">
      <!-- Left Panel - Configuration -->
      <div class="config-panel">
        <!-- Preset Selection -->
        <div class="preset-section">
          <h3>Quick Start Templates</h3>
          <div class="preset-grid">
            <div
              (click)="loadPreset(preset.id)"
              *ngFor="let preset of presetConfigurations"
              [class.selected]="selectedPreset === preset.id"
              class="preset-card">
              <div class="preset-header">
                <span class="preset-name">{{ preset.name }}</span>
                <p-tag
                  *ngIf="preset.isDefault"
                  severity="success"
                  styleClass="preset-tag"
                  value="Recommended">
                </p-tag>
              </div>
              <p class="preset-description">{{ preset.description }}</p>
              <div class="preset-metrics">
                <div
                  *ngFor="let metric of preset.metrics.slice(0, 3)"
                  [style.background-color]="metric.color + '20'"
                  [style.border-color]="metric.color + '40'"
                  class="preset-metric-chip">
                  {{ metric.label }}: {{ metric.weight }}%
                </div>
                <span *ngIf="preset.metrics.length > 3" class="more-metrics">
                  +{{ preset.metrics.length - 3 }} more
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Metrics Configuration -->
        <div class="metrics-section">
          <div class="section-header">
            <h3>Metric Weights</h3>
            <p-button
              (onClick)="resetToDefault()"
              [text]="true"
              icon="pi pi-refresh"
              label="Reset to Default"
              severity="secondary"
              size="small">
            </p-button>
          </div>

          <div class="metrics-list">
            <div
              *ngFor="let metric of workingConfig.metrics"
              [style.border-left-color]="metric.color"
              class="metric-item">

              <div class="metric-header">
                <div class="metric-info">
                  <div [style.background-color]="metric.color" class="metric-color-dot"></div>
                  <div class="metric-details">
                    <span class="metric-label">{{ metric.label }}</span>
                    <span class="metric-description">{{ metric.description }}</span>
                  </div>
                </div>
                <div class="metric-weight-display">
                  <span class="weight-percentage">{{ metric.weight }}%</span>
                </div>
              </div>

              <div class="metric-controls">
                <div class="slider-container">
                  <p-slider
                    (onSlideEnd)="onWeightChange(metric, metric.weight)"
                    [(ngModel)]="metric.weight"
                    [max]="100"
                    [min]="0"
                    [step]="1"
                    styleClass="custom-slider">
                  </p-slider>
                </div>
                <div class="weight-input">
                  <p-input-number
                    (onInput)="onWeightChange(metric, metric.weight)"
                    [(ngModel)]="metric.weight"
                    [max]="100"
                    [min]="0"
                    [step]="1"
                    size="small"
                    styleClass="weight-input-field"
                    suffix="%">
                  </p-input-number>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Preview -->
      <div class="preview-panel">
        <div class="preview-header">
          <h3>Live Preview</h3>
          <p>See how your changes affect performance scores</p>
        </div>

        <!-- Weight Distribution Chart -->
        <div class="weight-chart-section">
          <h4>Weight Distribution</h4>
          <div class="weight-chart">
            <div
              *ngFor="let metric of workingConfig.metrics"
              [pTooltip]="metric.label + ': ' + metric.weight + '%'"
              [style.background-color]="metric.color"
              [style.width.%]="metric.weight"
              class="weight-bar"
              tooltipPosition="top">
            </div>
          </div>
          <div class="weight-legend">
            <div
              *ngFor="let metric of workingConfig.metrics.slice(0, 4)"
              class="legend-item">
              <div [style.background-color]="metric.color" class="legend-color"></div>
              <span class="legend-label">{{ metric.label }}</span>
              <span class="legend-weight">{{ metric.weight }}%</span>
            </div>
          </div>
        </div>

        <!-- Sample Score Calculation -->
        <div *ngIf="previewData.length > 0" class="sample-calculation">
          <h4>Sample Calculations</h4>
          <div class="calculation-cards">
            <div
              *ngFor="let preview of previewData.slice(0, 3)"
              class="calculation-card">
              <div class="card-header">
                <span class="ad-name">{{ preview.adName }}</span>
                <div class="score-change">
                  <span class="old-score">{{ preview.currentScore }}%</span>
                  <i class="pi pi-arrow-right"></i>
                  <span [class.decreased]="preview.newScore < preview.currentScore" [class.improved]="preview.newScore > preview.currentScore"
                        class="new-score">
                    {{ preview.newScore }}%
                  </span>
                </div>
              </div>
              <div class="metric-breakdown">
                <div
                  *ngFor="let metric of workingConfig.metrics.slice(0, 3)"
                  class="breakdown-item">
                  <span class="breakdown-label">{{ metric.label }}</span>
                  <span class="breakdown-value">{{ preview.metrics[metric.metric] || 0 | number:'1.1-1' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Configuration Summary -->
        <div class="config-summary">
          <h4>Configuration Summary</h4>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">Active Metrics</span>
              <span class="stat-value">{{ getActiveMetrics() }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Primary Focus</span>
              <span class="stat-value">{{ getPrimaryMetric() }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Weight</span>
              <span [class.valid]="isValidConfiguration" class="stat-value">{{ totalWeight }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Actions -->
    <div class="builder-footer">
      <div class="footer-left">
        <p-button
          (onClick)="openSaveDialog()"
          [disabled]="!isValidConfiguration"
          icon="pi pi-save"
          label="Save Configuration"
          severity="secondary">
        </p-button>
      </div>
      <div class="footer-right">
        <p-button
          (onClick)="closeDialog()"
          [text]="true"
          icon="pi pi-times"
          label="Cancel"
          severity="secondary">
        </p-button>
        <p-button
          (onClick)="applyConfiguration()"
          [disabled]="!isValidConfiguration"
          icon="pi pi-check"
          label="Apply Changes"
          severity="success">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Save Configuration Dialog -->
  <p-dialog
    [(visible)]="showSaveDialog"
    [closable]="true"
    [draggable]="false"
    [modal]="true"
    [resizable]="false"
    [style]="{ width: '500px' }"
    header="Save Configuration"
    styleClass="save-config-dialog">

    <div class="save-form">
      <div class="form-field">
        <label for="configName">Configuration Name</label>
        <p-inputText
          [(ngModel)]="configName"
          id="configName"
          placeholder="Enter a name for this configuration"
          styleClass="w-full">
        </p-inputText>
      </div>

      <div class="form-field">
        <label for="configDescription">Description (Optional)</label>
        <p-inputText
          [(ngModel)]="configDescription"
          id="configDescription"
          placeholder="Describe when to use this configuration"
          styleClass="w-full">
        </p-inputText>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <p-button
        (onClick)="showSaveDialog = false"
        [text]="true"
        icon="pi pi-times"
        label="Cancel"
        severity="secondary">
      </p-button>
      <p-button
        (onClick)="saveConfiguration()"
        [disabled]="!configName.trim()"
        icon="pi pi-save"
        label="Save"
        severity="success">
      </p-button>
    </ng-template>
  </p-dialog>
</p-dialog>
