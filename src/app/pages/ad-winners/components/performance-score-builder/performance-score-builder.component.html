<p-dialog
  (onHide)="closeDialog()"
  [(visible)]="visible"
  [closable]="true"
  [draggable]="false"
  [maximizable]="false"
  [modal]="true"
  [resizable]="false"
  [style]="{ width: '100vw', height: '100vh', maxWidth: 'none', maxHeight: 'none' }"
  styleClass="fullscreen-score-builder">

  <ng-template pTemplate="header">
    <div class="fullscreen-header">
      <div class="header-icon">
        <i class="pi pi-chart-line"></i>
      </div>
      <div class="header-title">
        <h2>Performance Score Builder</h2>
        <p>Create your perfect performance scoring formula</p>
      </div>
    </div>
  </ng-template>

  <div class="fullscreen-content">
    <!-- Top Section: Predefined Templates -->
    <div class="templates-section">
      <h3>
        <i class="pi pi-bookmark"></i>
        Predefined Templates
      </h3>
      <div class="template-cards">
        <div
          *ngFor="let preset of presetConfigurations"
          class="template-card"
          [class.selected]="selectedPreset === preset.id"
          (click)="loadPreset(preset.id)">
          <div class="template-header">
            <h4>{{ preset.name }}</h4>
            <p-tag
              *ngIf="preset.isDefault"
              value="Recommended"
              severity="success"
              size="small">
            </p-tag>
          </div>
          <p class="template-description">{{ preset.description }}</p>
          <div class="template-preview">
            <div
              *ngFor="let metric of preset.metrics.filter(m => m.weight > 0).slice(0, 3)"
              class="metric-preview"
              [style.background-color]="metric.color + '20'"
              [style.border-color]="metric.color">
              <span class="metric-name">{{ metric.label }}</span>
              <span class="metric-weight">{{ metric.weight }}%</span>
            </div>
            <span *ngIf="preset.metrics.filter(m => m.weight > 0).length > 3" class="more-metrics">
              +{{ preset.metrics.filter(m => m.weight > 0).length - 3 }} more
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Metrics Configuration -->
    <div class="metrics-section">
      <div class="metrics-list">
        <div
          *ngFor="let metric of workingConfig.metrics"
          [style.border-left-color]="metric.color"
          class="metric-item">

          <div class="metric-header">
            <div class="metric-info">
              <div [style.background-color]="metric.color" class="metric-color-dot"></div>
              <div class="metric-details">
                <span class="metric-label">{{ metric.label }}</span>
                <span class="metric-description">{{ metric.description }}</span>
              </div>
            </div>
            <div class="metric-actions">
              <p-button
                *ngIf="metric.weight > 0"
                (onClick)="removeMetric(metric)"
                icon="pi pi-times"
                severity="danger"
                size="small"
                [text]="true"
                pTooltip="Remove metric"
                tooltipPosition="top">
              </p-button>
              <div class="metric-weight-display">
                <span class="weight-percentage">{{ metric.weight }}%</span>
              </div>
            </div>
          </div>

          <div class="metric-controls" *ngIf="metric.weight > 0">
            <div class="slider-container">
              <p-slider
                (onSlideEnd)="onWeightChange(metric, metric.weight)"
                [(ngModel)]="metric.weight"
                [max]="100"
                [min]="0"
                [step]="5"
                styleClass="custom-slider">
              </p-slider>
            </div>
            <div class="weight-input">
              <p-inputNumber
                (onInput)="onWeightChange(metric, metric.weight)"
                [(ngModel)]="metric.weight"
                [max]="100"
                [min]="0"
                [step]="5"
                size="small"
                styleClass="weight-input-field"
                suffix="%">
              </p-inputNumber>
            </div>
          </div>

          <div class="metric-add" *ngIf="metric.weight === 0">
            <p-button
              (onClick)="addMetric(metric)"
              icon="pi pi-plus"
              label="Add to formula"
              severity="success"
              size="small"
              [outlined]="true">
            </p-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Live Preview with Real Data -->
    <div class="preview-section" *ngIf="previewData.length > 0">
      <h4>Live Preview with Your Data</h4>
      <div class="preview-cards">
        <div
          *ngFor="let preview of previewData.slice(0, 3)"
          class="preview-card">
          <div class="preview-header">
            <span class="ad-name">{{ preview.adName }}</span>
            <div class="score-change">
              <span class="old-score">{{ preview.currentScore }}%</span>
              <i class="pi pi-arrow-right"></i>
              <span [class.decreased]="preview.newScore < preview.currentScore" [class.improved]="preview.newScore > preview.currentScore"
                    class="new-score">
                {{ preview.newScore }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Actions -->
    <div class="builder-footer">
      <p-button
        (onClick)="closeDialog()"
        [text]="true"
        icon="pi pi-times"
        label="Cancel"
        severity="secondary">
      </p-button>
      <p-button
        (onClick)="applyConfiguration()"
        [disabled]="!isValidConfiguration"
        icon="pi pi-check"
        label="Apply Changes"
        severity="success">
      </p-button>
    </div>
  </div>


</p-dialog>
