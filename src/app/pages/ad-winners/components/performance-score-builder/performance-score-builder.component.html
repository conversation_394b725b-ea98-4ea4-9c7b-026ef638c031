<p-dialog
  [(visible)]="visible"
  [modal]="true"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  [maximizable]="false"
  styleClass="score-builder-dialog"
  [style]="{ width: '95vw', maxWidth: '1200px', height: '90vh' }"
  header="Performance Score Builder"
  (onHide)="closeDialog()">

  <div class="score-builder-container">
    <!-- Header Section -->
    <div class="builder-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <i class="pi pi-chart-line"></i>
          </div>
          <div class="header-text">
            <h2>Customize Performance Scoring</h2>
            <p>Build your perfect performance score formula by adjusting metric weights</p>
          </div>
        </div>
        <div class="header-right">
          <div class="total-weight-indicator" [class.valid]="isValidConfiguration" [class.invalid]="!isValidConfiguration">
            <span class="weight-label">Total Weight</span>
            <span class="weight-value">{{ totalWeight }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="builder-content">
      <!-- Left Panel - Configuration -->
      <div class="config-panel">
        <!-- Preset Selection -->
        <div class="preset-section">
          <h3>Quick Start Templates</h3>
          <div class="preset-grid">
            <div 
              *ngFor="let preset of presetConfigurations" 
              class="preset-card"
              [class.selected]="selectedPreset === preset.id"
              (click)="loadPreset(preset.id)">
              <div class="preset-header">
                <span class="preset-name">{{ preset.name }}</span>
                <p-tag 
                  *ngIf="preset.isDefault" 
                  value="Recommended" 
                  severity="success" 
                  styleClass="preset-tag">
                </p-tag>
              </div>
              <p class="preset-description">{{ preset.description }}</p>
              <div class="preset-metrics">
                <div 
                  *ngFor="let metric of preset.metrics.slice(0, 3)" 
                  class="preset-metric-chip"
                  [style.background-color]="metric.color + '20'"
                  [style.border-color]="metric.color + '40'">
                  {{ metric.label }}: {{ metric.weight }}%
                </div>
                <span *ngIf="preset.metrics.length > 3" class="more-metrics">
                  +{{ preset.metrics.length - 3 }} more
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Metrics Configuration -->
        <div class="metrics-section">
          <div class="section-header">
            <h3>Metric Weights</h3>
            <p-button 
              label="Reset to Default" 
              icon="pi pi-refresh" 
              severity="secondary" 
              size="small"
              [text]="true"
              (onClick)="resetToDefault()">
            </p-button>
          </div>

          <div class="metrics-list">
            <div 
              *ngFor="let metric of workingConfig.metrics" 
              class="metric-item"
              [style.border-left-color]="metric.color">
              
              <div class="metric-header">
                <div class="metric-info">
                  <div class="metric-color-dot" [style.background-color]="metric.color"></div>
                  <div class="metric-details">
                    <span class="metric-label">{{ metric.label }}</span>
                    <span class="metric-description">{{ metric.description }}</span>
                  </div>
                </div>
                <div class="metric-weight-display">
                  <span class="weight-percentage">{{ metric.weight }}%</span>
                </div>
              </div>

              <div class="metric-controls">
                <div class="slider-container">
                  <p-slider
                    [(ngModel)]="metric.weight"
                    [min]="0"
                    [max]="100"
                    [step]="1"
                    styleClass="custom-slider"
                    (onSlideEnd)="onWeightChange(metric, metric.weight)">
                  </p-slider>
                </div>
                <div class="weight-input">
                  <p-inputNumber
                    [(ngModel)]="metric.weight"
                    [min]="0"
                    [max]="100"
                    [step]="1"
                    suffix="%"
                    size="small"
                    styleClass="weight-input-field"
                    (onInput)="onWeightChange(metric, metric.weight)">
                  </p-inputNumber>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Preview -->
      <div class="preview-panel">
        <div class="preview-header">
          <h3>Live Preview</h3>
          <p>See how your changes affect performance scores</p>
        </div>

        <!-- Weight Distribution Chart -->
        <div class="weight-chart-section">
          <h4>Weight Distribution</h4>
          <div class="weight-chart">
            <div 
              *ngFor="let metric of workingConfig.metrics" 
              class="weight-bar"
              [style.width.%]="metric.weight"
              [style.background-color]="metric.color"
              [pTooltip]="metric.label + ': ' + metric.weight + '%'"
              tooltipPosition="top">
            </div>
          </div>
          <div class="weight-legend">
            <div 
              *ngFor="let metric of workingConfig.metrics.slice(0, 4)" 
              class="legend-item">
              <div class="legend-color" [style.background-color]="metric.color"></div>
              <span class="legend-label">{{ metric.label }}</span>
              <span class="legend-weight">{{ metric.weight }}%</span>
            </div>
          </div>
        </div>

        <!-- Sample Score Calculation -->
        <div class="sample-calculation" *ngIf="previewData.length > 0">
          <h4>Sample Calculations</h4>
          <div class="calculation-cards">
            <div 
              *ngFor="let preview of previewData.slice(0, 3)" 
              class="calculation-card">
              <div class="card-header">
                <span class="ad-name">{{ preview.adName }}</span>
                <div class="score-change">
                  <span class="old-score">{{ preview.currentScore }}%</span>
                  <i class="pi pi-arrow-right"></i>
                  <span class="new-score" [class.improved]="preview.newScore > preview.currentScore" [class.decreased]="preview.newScore < preview.currentScore">
                    {{ preview.newScore }}%
                  </span>
                </div>
              </div>
              <div class="metric-breakdown">
                <div 
                  *ngFor="let metric of workingConfig.metrics.slice(0, 3)" 
                  class="breakdown-item">
                  <span class="breakdown-label">{{ metric.label }}</span>
                  <span class="breakdown-value">{{ preview.metrics[metric.metric] || 0 | number:'1.1-1' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Configuration Summary -->
        <div class="config-summary">
          <h4>Configuration Summary</h4>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">Active Metrics</span>
              <span class="stat-value">{{ workingConfig.metrics.filter(m => m.weight > 0).length }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Primary Focus</span>
              <span class="stat-value">{{ getPrimaryMetric() }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Weight</span>
              <span class="stat-value" [class.valid]="isValidConfiguration">{{ totalWeight }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Actions -->
    <div class="builder-footer">
      <div class="footer-left">
        <p-button 
          label="Save Configuration" 
          icon="pi pi-save" 
          severity="secondary"
          [disabled]="!isValidConfiguration"
          (onClick)="openSaveDialog()">
        </p-button>
      </div>
      <div class="footer-right">
        <p-button 
          label="Cancel" 
          icon="pi pi-times" 
          severity="secondary"
          [text]="true"
          (onClick)="closeDialog()">
        </p-button>
        <p-button 
          label="Apply Changes" 
          icon="pi pi-check" 
          severity="success"
          [disabled]="!isValidConfiguration"
          (onClick)="applyConfiguration()">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Save Configuration Dialog -->
  <p-dialog
    [(visible)]="showSaveDialog"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    styleClass="save-config-dialog"
    [style]="{ width: '500px' }"
    header="Save Configuration">
    
    <div class="save-form">
      <div class="form-field">
        <label for="configName">Configuration Name</label>
        <p-inputText
          id="configName"
          [(ngModel)]="configName"
          placeholder="Enter a name for this configuration"
          styleClass="w-full">
        </p-inputText>
      </div>
      
      <div class="form-field">
        <label for="configDescription">Description (Optional)</label>
        <p-inputText
          id="configDescription"
          [(ngModel)]="configDescription"
          placeholder="Describe when to use this configuration"
          styleClass="w-full">
        </p-inputText>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <p-button 
        label="Cancel" 
        icon="pi pi-times" 
        severity="secondary"
        [text]="true"
        (onClick)="showSaveDialog = false">
      </p-button>
      <p-button 
        label="Save" 
        icon="pi pi-save" 
        severity="success"
        [disabled]="!configName.trim()"
        (onClick)="saveConfiguration()">
      </p-button>
    </ng-template>
  </p-dialog>
</p-dialog>
