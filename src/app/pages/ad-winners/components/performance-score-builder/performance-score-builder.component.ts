import { Component, EventEmitter, Input, OnInit, Output, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { SliderModule } from 'primeng/slider';
import { ButtonModule } from 'primeng/button';
import { SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { DialogModule } from 'primeng/dialog';
import { TooltipModule } from 'primeng/tooltip';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { InputNumber } from 'primeng/inputnumber';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessageService, ConfirmationService } from 'primeng/api';
import { AdMediaComponent } from '../ad-media/ad-media.component';
import { PerformanceScoreConfigService } from '../../services/performance-score-config.service';
import {
  PerformanceScoreConfiguration,
  PerformanceScoreMetric,
  ALL_AVAILABLE_METRICS,
  PerformanceScoreConfigRequest
} from '../../models/ad-winner.model';

// Use the new models directly
type MetricWeight = PerformanceScoreMetric;
type ScoreConfiguration = PerformanceScoreConfiguration;

export interface ScorePreview {
  adName: string;
  currentScore: number;
  newScore: number;
  metrics: { [key: string]: number };
  videoUrl?: string | null;
  imageUrl?: string | null;
}

@Component({
  selector: 'chm-performance-score-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    SliderModule,
    ButtonModule,
    SelectModule,
    InputTextModule,
    DialogModule,
    TooltipModule,
    TagModule,
    ProgressBarModule,
    InputNumber,
    ToastModule,
    ConfirmDialogModule,
    AdMediaComponent,
  ],
  providers: [PerformanceScoreConfigService, MessageService, ConfirmationService],
  templateUrl: './performance-score-builder.component.html',
  styleUrls: ['./performance-score-builder.component.css'],
})
export class PerformanceScoreBuilderComponent implements OnInit, OnChanges {
  @Input() visible = false;
  @Input() currentConfiguration: ScoreConfiguration | null = null;
  @Input() previewData: ScorePreview[] = [];
  @Input() realAdData: any[] = []; // Real ad data for preview

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() configurationSaved = new EventEmitter<PerformanceScoreConfiguration>();
  @Output() configurationApplied = new EventEmitter<PerformanceScoreConfiguration>();

  // System and custom configurations
  systemConfigurations: PerformanceScoreConfiguration[] = [];
  customConfigurations: PerformanceScoreConfiguration[] = [];

  constructor(
    private configService: PerformanceScoreConfigService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private cdr: ChangeDetectorRef
  ) {}

  // Available metrics - loaded from service
  availableMetrics: PerformanceScoreMetric[] = [];

  // Current working configuration
  workingConfig: PerformanceScoreConfiguration = {
    name: 'Custom Configuration',
    description: 'Custom performance score configuration',
    metrics_config: {
      metrics: []
    }
  };

  // Preset configurations - will be initialized in ngOnInit
  presetConfigurations: PerformanceScoreConfiguration[] = [];

  selectedPreset: string | null = null;

  // Template creation form
  newTemplateName: string = '';
  newTemplateDescription: string = '';

  // Edit mode
  isEditMode: boolean = false;
  editingTemplateId: string | null = null;

  get totalWeight(): number {
    return this.availableMetrics.reduce(
      (sum, metric) => sum + metric.weight,
      0,
    );
  }

  get isValidConfiguration(): boolean {
    return (
      this.totalWeight === 100 &&
      this.availableMetrics.some((m) => m.weight > 0)
    );
  }

  ngOnInit(): void {
    this.loadAvailableMetrics();
    this.loadConfigurations();
    this.initializeWorkingConfig();
    this.loadDefaultTemplate();
    this.generatePreviewData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['visible'] && changes['visible'].currentValue === true) {
      // Reset to clean state when modal opens
      console.log('🔄 Modal opened - resetting to clean state');
      this.resetToInitialState();
      this.loadDefaultTemplate();
    }
  }

  loadAvailableMetrics(): void {
    this.availableMetrics = this.configService.getAvailableMetrics();
  }

  loadConfigurations(): void {
    this.configService.getConfigurations().subscribe(configs => {
      // Sort configurations to maintain consistent order
      const sortedConfigs = configs.sort((a, b) => {
        // First by creation date, then by name
        if (a.created_at && b.created_at) {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        }
        return a.name.localeCompare(b.name);
      });

      this.systemConfigurations = sortedConfigs.filter(c => c.is_system);
      this.customConfigurations = sortedConfigs.filter(c => !c.is_system);
      this.presetConfigurations = [...this.systemConfigurations, ...this.customConfigurations];

      console.log('📋 Loaded configurations:');
      console.log('System configs:', this.systemConfigurations.map(c => ({ id: c.id, name: c.name, is_default: c.is_default })));
      console.log('Custom configs:', this.customConfigurations.map(c => ({ id: c.id, name: c.name, is_default: c.is_default })));

      // After loading configurations, check if we need to load default
      this.loadDefaultTemplate();
    });
  }

  loadDefaultTemplate(): void {
    // Only load default if no preset is already selected
    if (this.selectedPreset) return;

    this.configService.getDefaultConfiguration().subscribe(defaultConfig => {
      if (defaultConfig && defaultConfig.id) {
        console.log('Loading default template:', defaultConfig.name);
        this.loadPreset(defaultConfig.id);
      } else {
        console.log('No default configuration found');
      }
    });
  }

  // This method is no longer needed as presets are loaded from Supabase

  initializeWorkingConfig(): void {
    if (this.currentConfiguration) {
      this.workingConfig = JSON.parse(
        JSON.stringify(this.currentConfiguration),
      );
    } else {
      // Use default preset
      this.loadPreset('ecommerce-focused');
    }
  }

  onWeightChange(metric: PerformanceScoreMetric, newWeight: number): void {
    metric.weight = newWeight;
    this.generatePreviewData();
  }

  normalizeWeights(): void {
    // Don't auto-normalize - let users control weights manually
    // This method is kept for compatibility but doesn't do automatic normalization
  }

  loadPreset(presetId: string): void {
    console.log('🔄 LOADING PRESET:', presetId);

    // Search in both system and custom configurations
    let preset = this.systemConfigurations.find((p) => p.id === presetId);
    console.log('Found in system configs:', !!preset);

    if (!preset) {
      preset = this.customConfigurations.find((p) => p.id === presetId);
      console.log('Found in custom configs:', !!preset);
    }

    if (preset) {
      console.log('✅ FOUND PRESET:', preset.name);
      console.log('Preset metrics:', preset.metrics_config.metrics.map(m => `${m.label}: ${m.weight}%`));

      // Reset all metrics to 0 first - PROPERLY!
      this.availableMetrics = this.configService.getAvailableMetrics();

      // Explicitly set ALL metrics to 0
      this.availableMetrics.forEach(metric => {
        metric.weight = 0;
      });
      console.log('✅ Reset ALL metrics to 0');
      console.log('Metrics after reset:', this.availableMetrics.filter(m => m.weight > 0).map(m => `${m.label}: ${m.weight}%`));

      // Apply only the preset metrics with their weights
      preset.metrics_config.metrics.forEach(presetMetric => {
        const availableMetric = this.availableMetrics.find(m => m.metric === presetMetric.metric);
        if (availableMetric) {
          availableMetric.weight = presetMetric.weight;
          console.log(`Set ${availableMetric.label} to ${presetMetric.weight}%`);
        } else {
          console.warn(`Metric not found: ${presetMetric.metric}`);
        }
      });

      this.selectedPreset = presetId;
      console.log('Set selectedPreset to:', presetId);

      this.exitEditMode(); // Exit edit mode when loading preset
      this.generatePreviewData();

      console.log('Final active metrics:', this.availableMetrics.filter(m => m.weight > 0).map(m => `${m.label}: ${m.weight}%`));
    } else {
      console.error('❌ PRESET NOT FOUND:', presetId);
      console.log('Available system IDs:', this.systemConfigurations.map(c => c.id));
      console.log('Available custom IDs:', this.customConfigurations.map(c => c.id));
    }
  }

  selectSystemTemplate(template: PerformanceScoreConfiguration): void {
    // Exit edit mode and load template
    this.exitEditMode();
    this.loadPreset(template.id!);
  }

  selectCustomTemplate(template: PerformanceScoreConfiguration): void {
    console.log('🎯 SELECTING CUSTOM TEMPLATE:', template.name, 'ID:', template.id);
    console.log('Template metrics:', template.metrics_config.metrics.map(m => `${m.label}: ${m.weight}%`));

    // Exit edit mode and load template
    this.exitEditMode();
    this.loadPreset(template.id!);
  }

  resetToDefault(): void {
    // First try to find default in system configurations
    let defaultConfig = this.systemConfigurations.find(c => c.is_default);

    // If not found, try custom configurations
    if (!defaultConfig) {
      defaultConfig = this.customConfigurations.find(c => c.is_default);
    }

    // If still not found, use the first system configuration as fallback
    if (!defaultConfig && this.systemConfigurations.length > 0) {
      defaultConfig = this.systemConfigurations[0];
    }

    if (defaultConfig) {
      console.log('🔄 Resetting to default:', defaultConfig.name);
      this.exitEditMode();
      this.loadPreset(defaultConfig.id!);

      this.messageService.add({
        severity: 'info',
        summary: 'Reset',
        detail: `Reset to ${defaultConfig.name}`
      });
    } else {
      console.warn('No default configuration found');
      this.messageService.add({
        severity: 'warn',
        summary: 'Warning',
        detail: 'No default configuration found'
      });
    }
  }

  applyConfiguration(): void {
    if (this.isValidConfiguration) {
      // Get the name from selected preset or create a custom name
      let configName = 'Custom Configuration';
      let configDescription = 'Custom performance score configuration';

      if (this.selectedPreset) {
        const selectedTemplate = this.presetConfigurations.find(p => p.id === this.selectedPreset);
        if (selectedTemplate) {
          configName = selectedTemplate.name;
          configDescription = selectedTemplate.description || configDescription;
        }
      }

      // Create a configuration object with current metrics
      const config: PerformanceScoreConfiguration = {
        name: configName,
        description: configDescription,
        metrics_config: {
          metrics: this.availableMetrics.filter(m => m.weight > 0)
        }
      };
      this.configurationApplied.emit(config);
      this.closeDialog();
    }
  }

  // These methods are replaced by saveAsCustomTemplate

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetToInitialState();
  }

  resetToInitialState(): void {
    // Clear template form
    this.clearTemplateForm();

    // Exit edit mode
    this.exitEditMode();

    // Clear selected preset
    this.selectedPreset = null;

    // Reset all metrics to 0
    this.availableMetrics = this.configService.getAvailableMetrics();

    // Regenerate preview with clean data
    this.generatePreviewData();

    console.log('🔄 Reset to initial state');
  }

  formatWeight(weight: number): string {
    return `${weight}%`;
  }

  getPrimaryMetric(): string {
    const sortedMetrics = this.availableMetrics
      .filter((m) => m.weight > 0)
      .sort((a, b) => b.weight - a.weight);

    return sortedMetrics.length > 0 ? sortedMetrics[0].label : 'None';
  }

  // Add/Remove metric methods
  addMetric(metric: PerformanceScoreMetric): void {
    metric.weight = 10; // Default weight when adding
    this.generatePreviewData();
  }

  removeMetric(metric: PerformanceScoreMetric): void {
    metric.weight = 0;
    this.generatePreviewData();
  }

  getActiveMetrics(): number {
    return this.availableMetrics.filter((m) => m.weight > 0).length;
  }

  // New weight control methods
  increaseWeight(metric: PerformanceScoreMetric): void {
    if (metric.weight < 95) {
      metric.weight = Math.min(metric.weight + 5, 100);
      this.onDirectWeightChange(metric, metric.weight);
    }
  }

  decreaseWeight(metric: PerformanceScoreMetric): void {
    if (metric.weight > 5) {
      metric.weight = Math.max(metric.weight - 5, 0);
      this.onDirectWeightChange(metric, metric.weight);
    }
  }

  onDirectWeightChange(metric: PerformanceScoreMetric, newWeight: number): void {
    metric.weight = newWeight;
    // Don't auto-normalize, let user control weights manually
    this.generatePreviewData();
  }

  // Preview helper methods
  getActiveMetricsForPreview(): PerformanceScoreMetric[] {
    return this.availableMetrics.filter((m) => m.weight > 0);
  }

  getAdType(preview: any): string {
    // Extract ad type from preview data
    return 'Video Ad'; // Placeholder - could be enhanced with real data
  }

  formatMetricValue(value: number, format: string): string {
    if (!value && value !== 0) return '-';

    switch (format) {
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return `$${value.toFixed(2)}`;
      case 'number':
        return value.toFixed(2);
      default:
        return value.toString();
    }
  }

  // Template helper methods
  getPresetActiveMetrics(preset: PerformanceScoreConfiguration): PerformanceScoreMetric[] {
    return preset.metrics_config.metrics.filter((m) => m.weight > 0);
  }

  getPresetPreviewMetrics(preset: PerformanceScoreConfiguration): PerformanceScoreMetric[] {
    return this.getPresetActiveMetrics(preset).slice(0, 3);
  }

  getPresetMoreMetricsCount(preset: PerformanceScoreConfiguration): number {
    const activeCount = this.getPresetActiveMetrics(preset).length;
    return Math.max(0, activeCount - 3);
  }

  hasMoreMetrics(preset: PerformanceScoreConfiguration): boolean {
    return this.getPresetMoreMetricsCount(preset) > 0;
  }

  // Score comparison helper methods
  getScoreChangeClass(preview: any): string {
    if (preview.newScore > preview.currentScore) {
      return 'improved';
    } else if (preview.newScore < preview.currentScore) {
      return 'decreased';
    }
    return '';
  }

  // Validation helper methods
  getValidationStatusClass(): string {
    return this.isValidConfiguration ? 'valid' : 'invalid';
  }

  getValidationIconClass(): string {
    return this.isValidConfiguration ? 'pi-check-circle' : 'pi-exclamation-triangle';
  }

  getValidationMessage(): string {
    return this.isValidConfiguration ? 'Formula is valid' : 'Total weight must equal 100%';
  }

  // Template card helper methods
  getTemplateCardClass(preset: PerformanceScoreConfiguration): string {
    return this.selectedPreset === preset.id ? 'selected' : '';
  }

  // Metric card helper methods
  getMetricCardClass(metric: PerformanceScoreMetric): string {
    if (metric.weight > 0) {
      return 'active';
    } else {
      return 'inactive';
    }
  }

  // Metric state helper methods
  isMetricActive(metric: PerformanceScoreMetric): boolean {
    return metric.weight > 0;
  }

  isMetricInactive(metric: PerformanceScoreMetric): boolean {
    return metric.weight === 0;
  }

  // Preview data helper methods
  hasPreviewData(): boolean {
    return this.previewData.length > 0;
  }

  // Button state helper methods
  isDecreaseDisabled(metric: PerformanceScoreMetric): boolean {
    return metric.weight <= 5;
  }

  isIncreaseDisabled(metric: PerformanceScoreMetric): boolean {
    return metric.weight >= 95;
  }

  // Template creation methods
  canCreateTemplate(): boolean {
    return this.newTemplateName.trim().length > 0 && this.isValidConfiguration;
  }

  createCustomTemplate(): void {
    if (!this.canCreateTemplate()) return;

    const config: PerformanceScoreConfigRequest = {
      name: this.newTemplateName.trim(),
      description: this.newTemplateDescription.trim() || `Custom template created on ${new Date().toLocaleDateString()}`,
      metrics_config: {
        metrics: this.availableMetrics.filter(m => m.weight > 0)
      }
    };

    const validation = this.configService.validateConfiguration(config);
    if (!validation.isValid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Invalid Configuration',
        detail: validation.errors.join(', ')
      });
      return;
    }

    this.configService.createConfiguration(config).subscribe(result => {
      if (result) {
        this.loadConfigurations();
        this.clearTemplateForm();
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Template created successfully!'
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to create template'
        });
      }
    });
  }

  setAsDefault(): void {
    if (!this.isValidConfiguration) return;

    // First create a temporary template if we don't have one
    if (!this.newTemplateName.trim()) {
      this.newTemplateName = `Default Formula - ${new Date().toLocaleDateString()}`;
    }

    const config: PerformanceScoreConfigRequest = {
      name: this.newTemplateName.trim(),
      description: this.newTemplateDescription.trim() || 'Default performance score configuration',
      metrics_config: {
        metrics: this.availableMetrics.filter(m => m.weight > 0)
      }
    };

    // Create the configuration first
    this.configService.createConfiguration(config).subscribe(result => {
      if (result && result.id) {
        // Then set it as default
        this.configService.setAsDefault(result.id).subscribe(success => {
          if (success) {
            this.loadConfigurations();
            this.clearTemplateForm();
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Configuration set as default successfully!'
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to set as default'
            });
          }
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to create configuration'
        });
      }
    });
  }

  saveEditedTemplate(): void {
    if (!this.editingTemplateId || !this.canCreateTemplate()) return;

    const config: PerformanceScoreConfigRequest = {
      name: this.newTemplateName.trim(),
      description: this.newTemplateDescription.trim(),
      metrics_config: {
        metrics: this.availableMetrics.filter(m => m.weight > 0)
      }
    };

    this.configService.updateConfiguration(this.editingTemplateId, config).subscribe(result => {
      if (result) {
        this.loadConfigurations();
        this.exitEditMode();
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Template updated successfully!'
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update template'
        });
      }
    });
  }

  exitEditMode(): void {
    this.isEditMode = false;
    this.editingTemplateId = null;
    this.clearTemplateForm();
  }

  setTemplateAsDefault(event: Event, template: PerformanceScoreConfiguration): void {
    event.stopPropagation();

    this.confirmationService.confirm({
      message: `Set "${template.name}" as the default template?`,
      header: 'Set Default Template',
      icon: 'pi pi-star',
      acceptButtonStyleClass: 'p-button-warning',
      accept: () => {
        this.configService.setAsDefault(template.id!).subscribe(success => {
          if (success) {
            // Update the local configurations immediately WITHOUT reloading
            this.systemConfigurations.forEach(config => {
              config.is_default = config.id === template.id;
            });
            this.customConfigurations.forEach(config => {
              config.is_default = config.id === template.id;
            });

            // Update presetConfigurations as well
            this.presetConfigurations.forEach(config => {
              config.is_default = config.id === template.id;
            });

            // Force change detection immediately
            this.cdr.detectChanges();

            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: `${template.name} set as default template!`
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to set as default'
            });
          }
        });
      }
    });
  }

  clearTemplateForm(): void {
    this.newTemplateName = '';
    this.newTemplateDescription = '';
  }

  // TrackBy functions for better performance
  trackByTemplateId(index: number, template: PerformanceScoreConfiguration): string {
    return template.id || index.toString();
  }

  // Old saveAsCustomTemplate method removed - now using createCustomTemplate

  editCustomTemplate(event: Event, template: PerformanceScoreConfiguration): void {
    event.stopPropagation();

    // Enter edit mode
    this.isEditMode = true;
    this.editingTemplateId = template.id!;
    this.newTemplateName = template.name;
    this.newTemplateDescription = template.description || '';

    // Load template metrics into working configuration
    this.availableMetrics = this.configService.getAvailableMetrics();
    template.metrics_config.metrics.forEach(templateMetric => {
      const availableMetric = this.availableMetrics.find(m => m.metric === templateMetric.metric);
      if (availableMetric) {
        availableMetric.weight = templateMetric.weight;
      }
    });

    this.selectedPreset = template.id!;
    this.generatePreviewData();

    this.messageService.add({
      severity: 'info',
      summary: 'Edit Mode',
      detail: 'Template loaded for editing'
    });
  }

  deleteCustomTemplate(event: Event, template: PerformanceScoreConfiguration): void {
    event.stopPropagation();

    this.confirmationService.confirm({
      message: `Are you sure you want to delete "${template.name}"?`,
      header: 'Delete Template',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.configService.deleteConfiguration(template.id!).subscribe(success => {
          if (success) {
            this.loadConfigurations();
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Template deleted successfully!'
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete template'
            });
          }
        });
      }
    });
  }

  clearAllCustomTemplates(): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete all custom templates? This action cannot be undone.',
      header: 'Delete All Templates',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        const deletePromises = this.customConfigurations.map(template =>
          this.configService.deleteConfiguration(template.id!)
        );

        // Wait for all deletions to complete
        Promise.all(deletePromises.map(obs => obs.toPromise())).then(() => {
          this.loadConfigurations();
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'All custom templates deleted!'
          });
        });
      }
    });
  }

  // Generate preview data using real ad data
  generatePreviewData(): void {
    if (this.realAdData && this.realAdData.length > 0) {
      // Use real ad data for preview
      this.previewData = this.realAdData.slice(0, 3).map((ad) => ({
        adName: ad.name,
        currentScore: ad.performance_score || 0,
        newScore: this.calculateSampleScore(ad.weekly_insights || {}),
        metrics: this.extractMetricsForDisplay(ad.weekly_insights || {}),
        videoUrl: ad.video_url || null,
        imageUrl: ad.image_url || null,
      }));
    } else {
      // Fallback to sample data
      this.previewData = [
        {
          adName: 'Sample Ad #1',
          currentScore: 78,
          newScore: this.calculateSampleScore({
            roas: 4.2,
            conversion_rate: 0.035,
            hook_rate: 0.45,
            hold_rate: 0.28,
            traffic_quality: 0.82,
            cpa: 25.5,
            avg_cpm: 12.3,
          }),
          metrics: {
            roas: 4.2,
            conversion_rate: 3.5,
            hook_rate: 45,
            hold_rate: 28,
            traffic_quality: 82,
            cpa: 25.5,
            avg_cpm: 12.3,
          },
          videoUrl: null,
          imageUrl: null,
        },
      ];
    }
  }

  private extractMetricsForDisplay(insights: any): { [key: string]: number } {
    return {
      roas: insights.roas || 0,
      conversion_rate: (insights.conversion_rate || 0) * 100,
      hook_rate: (insights.hook_rate || 0) * 100,
      hold_rate: (insights.hold_rate || 0) * 100,
      traffic_quality: (insights.traffic_quality || 0) * 100,
      cpa: insights.cpa || 0,
      avg_cpm: insights.avg_cpm || 0,
    };
  }

  // generateId method no longer needed - Supabase handles IDs

  private calculateSampleScore(metrics: any): number {
    let score = 0;
    let totalWeight = 0;

    this.availableMetrics.forEach((metric) => {
      const value = metrics[metric.metric];
      if (value !== null && value !== undefined) {
        let normalizedValue = 0;

        switch (metric.metric) {
          case 'roas':
            normalizedValue = Math.min(value * 10, 100);
            break;
          case 'conversion_rate':
          case 'hook_rate':
          case 'hold_rate':
          case 'traffic_quality':
          case 'outbound_ctr':
            normalizedValue = Math.min(value * 100, 100);
            break;
          case 'cpa':
            normalizedValue = Math.max(0, 100 - (value / 100) * 100);
            break;
          case 'avg_cpm':
            normalizedValue = Math.max(0, 100 - (value / 50) * 100);
            break;
        }

        score += normalizedValue * (metric.weight / 100);
        totalWeight += metric.weight;
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }
}
