import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { SliderModule } from 'primeng/slider';
import { ButtonModule } from 'primeng/button';
import { SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { DialogModule } from 'primeng/dialog';
import { TooltipModule } from 'primeng/tooltip';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';

export interface MetricWeight {
  metric: string;
  label: string;
  weight: number;
  description: string;
  format: 'percentage' | 'currency' | 'number';
  color: string;
}

export interface ScoreConfiguration {
  id: string;
  name: string;
  description: string;
  metrics: MetricWeight[];
  isDefault?: boolean;
  createdAt?: string;
}

export interface ScorePreview {
  adName: string;
  currentScore: number;
  newScore: number;
  metrics: { [key: string]: number };
}

@Component({
  selector: 'chm-performance-score-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    SliderModule,
    ButtonModule,
    SelectModule,
    InputTextModule,
    DialogModule,
    TooltipModule,
    TagModule,
    ProgressBarModule,
  ],
  templateUrl: './performance-score-builder.component.html',
  styleUrls: ['./performance-score-builder.component.css'],
})
export class PerformanceScoreBuilderComponent implements OnInit {
  @Input() visible = false;
  @Input() currentConfiguration: ScoreConfiguration | null = null;
  @Input() previewData: ScorePreview[] = [];
  
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() configurationSaved = new EventEmitter<ScoreConfiguration>();
  @Output() configurationApplied = new EventEmitter<ScoreConfiguration>();

  // Available metrics with default weights
  availableMetrics: MetricWeight[] = [
    {
      metric: 'roas',
      label: 'ROAS',
      weight: 25,
      description: 'Return on Ad Spend - Revenue generated per dollar spent',
      format: 'number',
      color: '#10b981'
    },
    {
      metric: 'conversion_rate',
      label: 'Conversion Rate',
      weight: 20,
      description: 'Percentage of visitors who complete a purchase',
      format: 'percentage',
      color: '#3b82f6'
    },
    {
      metric: 'hook_rate',
      label: 'Hook Rate',
      weight: 15,
      description: 'Percentage of people who watch first 3 seconds',
      format: 'percentage',
      color: '#8b5cf6'
    },
    {
      metric: 'hold_rate',
      label: 'Hold Rate',
      weight: 15,
      description: 'Percentage of viewers who watch beyond the hook',
      format: 'percentage',
      color: '#f59e0b'
    },
    {
      metric: 'traffic_quality',
      label: 'Traffic Quality',
      weight: 10,
      description: 'Landing page view rate from outbound clicks',
      format: 'percentage',
      color: '#ef4444'
    },
    {
      metric: 'cpa',
      label: 'CPA',
      weight: 10,
      description: 'Cost per Acquisition - Lower is better',
      format: 'currency',
      color: '#06b6d4'
    },
    {
      metric: 'avg_cpm',
      label: 'CPM',
      weight: 5,
      description: 'Cost per thousand impressions - Lower is better',
      format: 'currency',
      color: '#84cc16'
    }
  ];

  // Current working configuration
  workingConfig: ScoreConfiguration = {
    id: '',
    name: '',
    description: '',
    metrics: []
  };

  // Preset configurations - will be initialized in ngOnInit
  presetConfigurations: ScoreConfiguration[] = [];

  selectedPreset: string = '';
  configName = '';
  configDescription = '';
  showSaveDialog = false;

  ngOnInit(): void {
    this.initializeWorkingConfig();
    this.generatePreviewData();
  }

  initializeWorkingConfig(): void {
    if (this.currentConfiguration) {
      this.workingConfig = JSON.parse(JSON.stringify(this.currentConfiguration));
    } else {
      // Use default preset
      this.loadPreset('ecommerce-focused');
    }
  }

  get totalWeight(): number {
    return this.workingConfig.metrics.reduce((sum, metric) => sum + metric.weight, 0);
  }

  get isValidConfiguration(): boolean {
    return this.totalWeight === 100 && this.workingConfig.metrics.some(m => m.weight > 0);
  }

  onWeightChange(metric: MetricWeight, newWeight: number): void {
    metric.weight = newWeight;
    this.normalizeWeights();
    this.generatePreviewData();
  }

  normalizeWeights(): void {
    const total = this.totalWeight;
    if (total > 100) {
      // Proportionally reduce all weights
      const factor = 100 / total;
      this.workingConfig.metrics.forEach(metric => {
        metric.weight = Math.round(metric.weight * factor);
      });
    }
  }

  loadPreset(presetId: string): void {
    const preset = this.presetConfigurations.find(p => p.id === presetId);
    if (preset) {
      this.workingConfig = JSON.parse(JSON.stringify(preset));
      this.selectedPreset = presetId;
    }
  }

  resetToDefault(): void {
    this.loadPreset('ecommerce-focused');
  }

  applyConfiguration(): void {
    if (this.isValidConfiguration) {
      this.configurationApplied.emit(this.workingConfig);
      this.closeDialog();
    }
  }

  openSaveDialog(): void {
    this.configName = this.workingConfig.name || '';
    this.configDescription = this.workingConfig.description || '';
    this.showSaveDialog = true;
  }

  saveConfiguration(): void {
    if (this.configName.trim() && this.isValidConfiguration) {
      const config: ScoreConfiguration = {
        ...this.workingConfig,
        id: this.generateId(),
        name: this.configName.trim(),
        description: this.configDescription.trim(),
        createdAt: new Date().toISOString()
      };
      
      this.configurationSaved.emit(config);
      this.showSaveDialog = false;
      this.closeDialog();
    }
  }

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  private generateId(): string {
    return 'config-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  getMetricColor(metric: MetricWeight): string {
    return metric.color;
  }

  formatWeight(weight: number): string {
    return `${weight}%`;
  }

  getPrimaryMetric(): string {
    const sortedMetrics = this.workingConfig.metrics
      .filter(m => m.weight > 0)
      .sort((a, b) => b.weight - a.weight);

    return sortedMetrics.length > 0 ? sortedMetrics[0].label : 'None';
  }

  // Generate preview data for sample calculations
  generatePreviewData(): void {
    // This would typically come from actual ad data
    // For now, we'll create sample data
    this.previewData = [
      {
        adName: 'Summer Sale Campaign #1',
        currentScore: 78,
        newScore: this.calculateSampleScore({
          roas: 4.2,
          conversion_rate: 0.035,
          hook_rate: 0.45,
          hold_rate: 0.28,
          traffic_quality: 0.82,
          cpa: 25.50,
          avg_cpm: 12.30
        }),
        metrics: {
          roas: 4.2,
          conversion_rate: 3.5,
          hook_rate: 45,
          hold_rate: 28,
          traffic_quality: 82,
          cpa: 25.50,
          avg_cpm: 12.30
        }
      },
      {
        adName: 'Product Launch Video',
        currentScore: 65,
        newScore: this.calculateSampleScore({
          roas: 3.1,
          conversion_rate: 0.028,
          hook_rate: 0.62,
          hold_rate: 0.41,
          traffic_quality: 0.75,
          cpa: 32.80,
          avg_cpm: 15.20
        }),
        metrics: {
          roas: 3.1,
          conversion_rate: 2.8,
          hook_rate: 62,
          hold_rate: 41,
          traffic_quality: 75,
          cpa: 32.80,
          avg_cpm: 15.20
        }
      },
      {
        adName: 'Brand Awareness Creative',
        currentScore: 82,
        newScore: this.calculateSampleScore({
          roas: 2.8,
          conversion_rate: 0.022,
          hook_rate: 0.71,
          hold_rate: 0.55,
          traffic_quality: 0.68,
          cpa: 45.20,
          avg_cpm: 8.90
        }),
        metrics: {
          roas: 2.8,
          conversion_rate: 2.2,
          hook_rate: 71,
          hold_rate: 55,
          traffic_quality: 68,
          cpa: 45.20,
          avg_cpm: 8.90
        }
      }
    ];
  }

  private calculateSampleScore(metrics: any): number {
    let score = 0;
    let totalWeight = 0;

    this.workingConfig.metrics.forEach(metric => {
      const value = metrics[metric.metric];
      if (value !== null && value !== undefined) {
        let normalizedValue = 0;

        switch (metric.metric) {
          case 'roas':
            normalizedValue = Math.min(value * 10, 100);
            break;
          case 'conversion_rate':
          case 'hook_rate':
          case 'hold_rate':
          case 'traffic_quality':
          case 'outbound_ctr':
            normalizedValue = Math.min(value * 100, 100);
            break;
          case 'cpa':
            normalizedValue = Math.max(0, 100 - (value / 100) * 100);
            break;
          case 'avg_cpm':
            normalizedValue = Math.max(0, 100 - (value / 50) * 100);
            break;
        }

        score += normalizedValue * (metric.weight / 100);
        totalWeight += metric.weight;
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }
}
