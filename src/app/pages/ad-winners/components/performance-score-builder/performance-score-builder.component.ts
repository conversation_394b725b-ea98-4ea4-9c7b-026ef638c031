import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { SliderModule } from 'primeng/slider';
import { ButtonModule } from 'primeng/button';
import { SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { DialogModule } from 'primeng/dialog';
import { TooltipModule } from 'primeng/tooltip';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';

export interface MetricWeight {
  metric: string;
  label: string;
  weight: number;
  description: string;
  format: 'percentage' | 'currency' | 'number';
  color: string;
}

export interface ScoreConfiguration {
  id: string;
  name: string;
  description: string;
  metrics: MetricWeight[];
  isDefault?: boolean;
  createdAt?: string;
}

export interface ScorePreview {
  adName: string;
  currentScore: number;
  newScore: number;
  metrics: { [key: string]: number };
}

@Component({
  selector: 'chm-performance-score-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    SliderModule,
    ButtonModule,
    SelectModule,
    InputTextModule,
    DialogModule,
    TooltipModule,
    TagModule,
    ProgressBarModule,
  ],
  templateUrl: './performance-score-builder.component.html',
  styleUrls: ['./performance-score-builder.component.css'],
})
export class PerformanceScoreBuilderComponent implements OnInit {
  @Input() visible = false;
  @Input() currentConfiguration: ScoreConfiguration | null = null;
  @Input() previewData: ScorePreview[] = [];
  
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() configurationSaved = new EventEmitter<ScoreConfiguration>();
  @Output() configurationApplied = new EventEmitter<ScoreConfiguration>();

  // Available metrics with default weights
  availableMetrics: MetricWeight[] = [
    {
      metric: 'roas',
      label: 'ROAS',
      weight: 25,
      description: 'Return on Ad Spend - Revenue generated per dollar spent',
      format: 'number',
      color: '#10b981'
    },
    {
      metric: 'conversion_rate',
      label: 'Conversion Rate',
      weight: 20,
      description: 'Percentage of visitors who complete a purchase',
      format: 'percentage',
      color: '#3b82f6'
    },
    {
      metric: 'hook_rate',
      label: 'Hook Rate',
      weight: 15,
      description: 'Percentage of people who watch first 3 seconds',
      format: 'percentage',
      color: '#8b5cf6'
    },
    {
      metric: 'hold_rate',
      label: 'Hold Rate',
      weight: 15,
      description: 'Percentage of viewers who watch beyond the hook',
      format: 'percentage',
      color: '#f59e0b'
    },
    {
      metric: 'traffic_quality',
      label: 'Traffic Quality',
      weight: 10,
      description: 'Landing page view rate from outbound clicks',
      format: 'percentage',
      color: '#ef4444'
    },
    {
      metric: 'cpa',
      label: 'CPA',
      weight: 10,
      description: 'Cost per Acquisition - Lower is better',
      format: 'currency',
      color: '#06b6d4'
    },
    {
      metric: 'avg_cpm',
      label: 'CPM',
      weight: 5,
      description: 'Cost per thousand impressions - Lower is better',
      format: 'currency',
      color: '#84cc16'
    }
  ];

  // Current working configuration
  workingConfig: ScoreConfiguration = {
    id: '',
    name: '',
    description: '',
    metrics: []
  };

  // Preset configurations
  presetConfigurations: ScoreConfiguration[] = [
    {
      id: 'ecommerce-focused',
      name: 'E-commerce Focused',
      description: 'Optimized for online sales with emphasis on ROAS and conversion rate',
      isDefault: true,
      metrics: [
        { ...this.availableMetrics[0], weight: 35 }, // ROAS
        { ...this.availableMetrics[1], weight: 30 }, // Conversion Rate
        { ...this.availableMetrics[4], weight: 15 }, // Traffic Quality
        { ...this.availableMetrics[5], weight: 10 }, // CPA
        { ...this.availableMetrics[2], weight: 5 },  // Hook Rate
        { ...this.availableMetrics[3], weight: 5 },  // Hold Rate
        { ...this.availableMetrics[6], weight: 0 }   // CPM
      ]
    },
    {
      id: 'brand-awareness',
      name: 'Brand Awareness',
      description: 'Focus on reach and engagement for brand building campaigns',
      metrics: [
        { ...this.availableMetrics[2], weight: 30 }, // Hook Rate
        { ...this.availableMetrics[3], weight: 25 }, // Hold Rate
        { ...this.availableMetrics[6], weight: 20 }, // CPM
        { ...this.availableMetrics[4], weight: 15 }, // Traffic Quality
        { ...this.availableMetrics[0], weight: 10 }, // ROAS
        { ...this.availableMetrics[1], weight: 0 },  // Conversion Rate
        { ...this.availableMetrics[5], weight: 0 }   // CPA
      ]
    },
    {
      id: 'balanced',
      name: 'Balanced Performance',
      description: 'Equal weight across all key performance indicators',
      metrics: this.availableMetrics.map(m => ({ ...m, weight: Math.round(100 / this.availableMetrics.length) }))
    }
  ];

  selectedPreset: string = '';
  configName = '';
  configDescription = '';
  showSaveDialog = false;

  ngOnInit(): void {
    this.initializeWorkingConfig();
  }

  initializeWorkingConfig(): void {
    if (this.currentConfiguration) {
      this.workingConfig = JSON.parse(JSON.stringify(this.currentConfiguration));
    } else {
      // Use default preset
      this.loadPreset('ecommerce-focused');
    }
  }

  get totalWeight(): number {
    return this.workingConfig.metrics.reduce((sum, metric) => sum + metric.weight, 0);
  }

  get isValidConfiguration(): boolean {
    return this.totalWeight === 100 && this.workingConfig.metrics.some(m => m.weight > 0);
  }

  onWeightChange(metric: MetricWeight, newWeight: number): void {
    metric.weight = newWeight;
    this.normalizeWeights();
  }

  normalizeWeights(): void {
    const total = this.totalWeight;
    if (total > 100) {
      // Proportionally reduce all weights
      const factor = 100 / total;
      this.workingConfig.metrics.forEach(metric => {
        metric.weight = Math.round(metric.weight * factor);
      });
    }
  }

  loadPreset(presetId: string): void {
    const preset = this.presetConfigurations.find(p => p.id === presetId);
    if (preset) {
      this.workingConfig = JSON.parse(JSON.stringify(preset));
      this.selectedPreset = presetId;
    }
  }

  resetToDefault(): void {
    this.loadPreset('ecommerce-focused');
  }

  applyConfiguration(): void {
    if (this.isValidConfiguration) {
      this.configurationApplied.emit(this.workingConfig);
      this.closeDialog();
    }
  }

  openSaveDialog(): void {
    this.configName = this.workingConfig.name || '';
    this.configDescription = this.workingConfig.description || '';
    this.showSaveDialog = true;
  }

  saveConfiguration(): void {
    if (this.configName.trim() && this.isValidConfiguration) {
      const config: ScoreConfiguration = {
        ...this.workingConfig,
        id: this.generateId(),
        name: this.configName.trim(),
        description: this.configDescription.trim(),
        createdAt: new Date().toISOString()
      };
      
      this.configurationSaved.emit(config);
      this.showSaveDialog = false;
      this.closeDialog();
    }
  }

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  private generateId(): string {
    return 'config-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  getMetricColor(metric: MetricWeight): string {
    return metric.color;
  }

  formatWeight(weight: number): string {
    return `${weight}%`;
  }
}
