import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { SliderModule } from 'primeng/slider';
import { ButtonModule } from 'primeng/button';
import { SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { DialogModule } from 'primeng/dialog';
import { TooltipModule } from 'primeng/tooltip';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { InputNumber } from 'primeng/inputnumber';
import { PerformanceScoreConfigService } from '../../services/performance-score-config.service';
import {
  PerformanceScoreConfiguration,
  PerformanceScoreMetric,
  ALL_AVAILABLE_METRICS,
  PerformanceScoreConfigRequest
} from '../../models/ad-winner.model';

export interface MetricWeight {
  metric: string;
  label: string;
  weight: number;
  description: string;
  format: 'percentage' | 'currency' | 'number';
  color: string;
}

export interface ScoreConfiguration {
  id: string;
  name: string;
  description: string;
  metrics: MetricWeight[];
  isDefault?: boolean;
  createdAt?: string;
}

export interface ScorePreview {
  adName: string;
  currentScore: number;
  newScore: number;
  metrics: { [key: string]: number };
}

@Component({
  selector: 'chm-performance-score-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    SliderModule,
    ButtonModule,
    SelectModule,
    InputTextModule,
    DialogModule,
    TooltipModule,
    TagModule,
    ProgressBarModule,
    InputNumber,
  ],
  templateUrl: './performance-score-builder.component.html',
  styleUrls: ['./performance-score-builder.component.css'],
})
export class PerformanceScoreBuilderComponent implements OnInit {
  @Input() visible = false;
  @Input() currentConfiguration: ScoreConfiguration | null = null;
  @Input() previewData: ScorePreview[] = [];
  @Input() realAdData: any[] = []; // Real ad data for preview

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() configurationSaved = new EventEmitter<ScoreConfiguration>();
  @Output() configurationApplied = new EventEmitter<ScoreConfiguration>();

  // Available metrics with default weights
  availableMetrics: MetricWeight[] = [
    {
      metric: 'roas',
      label: 'ROAS',
      weight: 25,
      description: 'Return on Ad Spend - Revenue generated per dollar spent',
      format: 'number',
      color: '#10b981',
    },
    {
      metric: 'conversion_rate',
      label: 'Conversion Rate',
      weight: 20,
      description: 'Percentage of visitors who complete a purchase',
      format: 'percentage',
      color: '#3b82f6',
    },
    {
      metric: 'hook_rate',
      label: 'Hook Rate',
      weight: 15,
      description: 'Percentage of people who watch first 3 seconds',
      format: 'percentage',
      color: '#8b5cf6',
    },
    {
      metric: 'hold_rate',
      label: 'Hold Rate',
      weight: 15,
      description: 'Percentage of viewers who watch beyond the hook',
      format: 'percentage',
      color: '#f59e0b',
    },
    {
      metric: 'traffic_quality',
      label: 'Traffic Quality',
      weight: 10,
      description: 'Landing page view rate from outbound clicks',
      format: 'percentage',
      color: '#ef4444',
    },
    {
      metric: 'cpa',
      label: 'CPA',
      weight: 10,
      description: 'Cost per Acquisition - Lower is better',
      format: 'currency',
      color: '#06b6d4',
    },
    {
      metric: 'avg_cpm',
      label: 'CPM',
      weight: 5,
      description: 'Cost per thousand impressions - Lower is better',
      format: 'currency',
      color: '#84cc16',
    },
  ];

  // Current working configuration
  workingConfig: ScoreConfiguration = {
    id: '',
    name: '',
    description: '',
    metrics: [],
  };

  // Preset configurations - will be initialized in ngOnInit
  presetConfigurations: ScoreConfiguration[] = [];

  selectedPreset: string = '';
  configName = '';
  configDescription = '';
  showSaveDialog = false;

  get totalWeight(): number {
    return this.workingConfig.metrics.reduce(
      (sum, metric) => sum + metric.weight,
      0,
    );
  }

  get isValidConfiguration(): boolean {
    return (
      this.totalWeight === 100 &&
      this.workingConfig.metrics.some((m) => m.weight > 0)
    );
  }

  ngOnInit(): void {
    this.initializePresetConfigurations();
    this.initializeWorkingConfig();
    this.generatePreviewData();
  }

  initializePresetConfigurations(): void {
    this.presetConfigurations = [
      {
        id: 'ecommerce-focused',
        name: 'E-commerce Focused',
        description:
          'Optimized for online sales with emphasis on ROAS and conversion rate',
        isDefault: true,
        metrics: [
          { ...this.availableMetrics[0], weight: 35 }, // ROAS
          { ...this.availableMetrics[1], weight: 30 }, // Conversion Rate
          { ...this.availableMetrics[4], weight: 15 }, // Traffic Quality
          { ...this.availableMetrics[5], weight: 10 }, // CPA
          { ...this.availableMetrics[2], weight: 5 }, // Hook Rate
          { ...this.availableMetrics[3], weight: 5 }, // Hold Rate
          { ...this.availableMetrics[6], weight: 0 }, // CPM
        ],
      },
      {
        id: 'brand-awareness',
        name: 'Brand Awareness',
        description:
          'Focus on reach and engagement for brand building campaigns',
        metrics: [
          { ...this.availableMetrics[2], weight: 30 }, // Hook Rate
          { ...this.availableMetrics[3], weight: 25 }, // Hold Rate
          { ...this.availableMetrics[6], weight: 20 }, // CPM
          { ...this.availableMetrics[4], weight: 15 }, // Traffic Quality
          { ...this.availableMetrics[0], weight: 10 }, // ROAS
          { ...this.availableMetrics[1], weight: 0 }, // Conversion Rate
          { ...this.availableMetrics[5], weight: 0 }, // CPA
        ],
      },
      {
        id: 'balanced',
        name: 'Balanced Performance',
        description: 'Equal weight across all key performance indicators',
        metrics: this.availableMetrics.map((m) => ({
          ...m,
          weight: Math.round(100 / this.availableMetrics.length),
        })),
      },
    ];
  }

  initializeWorkingConfig(): void {
    if (this.currentConfiguration) {
      this.workingConfig = JSON.parse(
        JSON.stringify(this.currentConfiguration),
      );
    } else {
      // Use default preset
      this.loadPreset('ecommerce-focused');
    }
  }

  onWeightChange(metric: MetricWeight, newWeight: number): void {
    metric.weight = newWeight;
    this.generatePreviewData();
  }

  normalizeWeights(): void {
    // Don't auto-normalize - let users control weights manually
    // This method is kept for compatibility but doesn't do automatic normalization
  }

  loadPreset(presetId: string): void {
    const preset = this.presetConfigurations.find((p) => p.id === presetId);
    if (preset) {
      this.workingConfig = JSON.parse(JSON.stringify(preset));
      this.selectedPreset = presetId;
    }
  }

  resetToDefault(): void {
    this.loadPreset('ecommerce-focused');
  }

  applyConfiguration(): void {
    if (this.isValidConfiguration) {
      this.configurationApplied.emit(this.workingConfig);
      this.closeDialog();
    }
  }

  openSaveDialog(): void {
    this.configName = this.workingConfig.name || '';
    this.configDescription = this.workingConfig.description || '';
    this.showSaveDialog = true;
  }

  saveConfiguration(): void {
    if (this.configName.trim() && this.isValidConfiguration) {
      const config: ScoreConfiguration = {
        ...this.workingConfig,
        id: this.generateId(),
        name: this.configName.trim(),
        description: this.configDescription.trim(),
        createdAt: new Date().toISOString(),
      };

      this.configurationSaved.emit(config);
      this.showSaveDialog = false;
      this.closeDialog();
    }
  }

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  getMetricColor(metric: MetricWeight): string {
    return metric.color;
  }

  formatWeight(weight: number): string {
    return `${weight}%`;
  }

  getPrimaryMetric(): string {
    const sortedMetrics = this.workingConfig.metrics
      .filter((m) => m.weight > 0)
      .sort((a, b) => b.weight - a.weight);

    return sortedMetrics.length > 0 ? sortedMetrics[0].label : 'None';
  }

  // Add/Remove metric methods
  addMetric(metric: MetricWeight): void {
    metric.weight = 10; // Default weight when adding
    this.generatePreviewData();
  }

  removeMetric(metric: MetricWeight): void {
    metric.weight = 0;
    this.generatePreviewData();
  }

  getActiveMetrics(): number {
    return this.workingConfig.metrics.filter((m) => m.weight > 0).length;
  }

  // New weight control methods
  increaseWeight(metric: MetricWeight): void {
    if (metric.weight < 95) {
      metric.weight = Math.min(metric.weight + 5, 100);
      this.onDirectWeightChange(metric, metric.weight);
    }
  }

  decreaseWeight(metric: MetricWeight): void {
    if (metric.weight > 5) {
      metric.weight = Math.max(metric.weight - 5, 0);
      this.onDirectWeightChange(metric, metric.weight);
    }
  }

  onDirectWeightChange(metric: MetricWeight, newWeight: number): void {
    metric.weight = newWeight;
    // Don't auto-normalize, let user control weights manually
    this.generatePreviewData();
  }

  // Preview helper methods
  getActiveMetricsForPreview(): MetricWeight[] {
    return this.workingConfig.metrics.filter((m) => m.weight > 0);
  }

  getAdType(preview: any): string {
    // Extract ad type from preview data
    return 'Video Ad'; // Placeholder - could be enhanced with real data
  }

  formatMetricValue(value: number, format: string): string {
    if (!value && value !== 0) return '-';

    switch (format) {
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return `$${value.toFixed(2)}`;
      case 'number':
        return value.toFixed(2);
      default:
        return value.toString();
    }
  }

  // Template helper methods
  getPresetActiveMetrics(preset: ScoreConfiguration): MetricWeight[] {
    return preset.metrics.filter((m) => m.weight > 0);
  }

  getPresetPreviewMetrics(preset: ScoreConfiguration): MetricWeight[] {
    return this.getPresetActiveMetrics(preset).slice(0, 3);
  }

  getPresetMoreMetricsCount(preset: ScoreConfiguration): number {
    const activeCount = this.getPresetActiveMetrics(preset).length;
    return Math.max(0, activeCount - 3);
  }

  hasMoreMetrics(preset: ScoreConfiguration): boolean {
    return this.getPresetMoreMetricsCount(preset) > 0;
  }

  // Score comparison helper methods
  getScoreChangeClass(preview: any): string {
    if (preview.newScore > preview.currentScore) {
      return 'improved';
    } else if (preview.newScore < preview.currentScore) {
      return 'decreased';
    }
    return '';
  }

  // Validation helper methods
  getValidationStatusClass(): string {
    return this.isValidConfiguration ? 'valid' : 'invalid';
  }

  getValidationIconClass(): string {
    return this.isValidConfiguration ? 'pi-check-circle' : 'pi-exclamation-triangle';
  }

  getValidationMessage(): string {
    return this.isValidConfiguration ? 'Formula is valid' : 'Total weight must equal 100%';
  }

  // Template card helper methods
  getTemplateCardClass(preset: ScoreConfiguration): string {
    return this.selectedPreset === preset.id ? 'selected' : '';
  }

  // Metric card helper methods
  getMetricCardClass(metric: MetricWeight): string {
    if (metric.weight > 0) {
      return 'active';
    } else {
      return 'inactive';
    }
  }

  // Metric state helper methods
  isMetricActive(metric: MetricWeight): boolean {
    return metric.weight > 0;
  }

  isMetricInactive(metric: MetricWeight): boolean {
    return metric.weight === 0;
  }

  // Preview data helper methods
  hasPreviewData(): boolean {
    return this.previewData.length > 0;
  }

  // Button state helper methods
  isDecreaseDisabled(metric: MetricWeight): boolean {
    return metric.weight <= 5;
  }

  isIncreaseDisabled(metric: MetricWeight): boolean {
    return metric.weight >= 95;
  }

  // Generate preview data using real ad data
  generatePreviewData(): void {
    if (this.realAdData && this.realAdData.length > 0) {
      // Use real ad data for preview
      this.previewData = this.realAdData.slice(0, 3).map((ad) => ({
        adName: ad.name,
        currentScore: ad.performance_score || 0,
        newScore: this.calculateSampleScore(ad.weekly_insights || {}),
        metrics: this.extractMetricsForDisplay(ad.weekly_insights || {}),
      }));
    } else {
      // Fallback to sample data
      this.previewData = [
        {
          adName: 'Sample Ad #1',
          currentScore: 78,
          newScore: this.calculateSampleScore({
            roas: 4.2,
            conversion_rate: 0.035,
            hook_rate: 0.45,
            hold_rate: 0.28,
            traffic_quality: 0.82,
            cpa: 25.5,
            avg_cpm: 12.3,
          }),
          metrics: {
            roas: 4.2,
            conversion_rate: 3.5,
            hook_rate: 45,
            hold_rate: 28,
            traffic_quality: 82,
            cpa: 25.5,
            avg_cpm: 12.3,
          },
        },
      ];
    }
  }

  private extractMetricsForDisplay(insights: any): { [key: string]: number } {
    return {
      roas: insights.roas || 0,
      conversion_rate: (insights.conversion_rate || 0) * 100,
      hook_rate: (insights.hook_rate || 0) * 100,
      hold_rate: (insights.hold_rate || 0) * 100,
      traffic_quality: (insights.traffic_quality || 0) * 100,
      cpa: insights.cpa || 0,
      avg_cpm: insights.avg_cpm || 0,
    };
  }

  private generateId(): string {
    return (
      'config-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9)
    );
  }

  private calculateSampleScore(metrics: any): number {
    let score = 0;
    let totalWeight = 0;

    this.workingConfig.metrics.forEach((metric) => {
      const value = metrics[metric.metric];
      if (value !== null && value !== undefined) {
        let normalizedValue = 0;

        switch (metric.metric) {
          case 'roas':
            normalizedValue = Math.min(value * 10, 100);
            break;
          case 'conversion_rate':
          case 'hook_rate':
          case 'hold_rate':
          case 'traffic_quality':
          case 'outbound_ctr':
            normalizedValue = Math.min(value * 100, 100);
            break;
          case 'cpa':
            normalizedValue = Math.max(0, 100 - (value / 100) * 100);
            break;
          case 'avg_cpm':
            normalizedValue = Math.max(0, 100 - (value / 50) * 100);
            break;
        }

        score += normalizedValue * (metric.weight / 100);
        totalWeight += metric.weight;
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }
}
