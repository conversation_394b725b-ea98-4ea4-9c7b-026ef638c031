/* Dialog Styling */
:host ::ng-deep .score-builder-dialog .p-dialog {
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(85, 33, 190, 0.1);
  overflow: hidden;
}

:host ::ng-deep .score-builder-dialog .p-dialog-header {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 1.25rem;
}

:host ::ng-deep .score-builder-dialog .p-dialog-content {
  padding: 0;
  background: #fefefe;
}

/* Main Container */
.score-builder-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Poppins', sans-serif;
}

/* Header Section */
.builder-header {
  background: linear-gradient(135deg, rgba(254, 236, 249, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border-bottom: 1px solid rgba(85, 33, 190, 0.1);
  padding: 1.5rem 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.header-text h2 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.header-text p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

.total-weight-indicator {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 0.75rem 1.25rem;
  text-align: center;
  transition: all 0.3s ease;
}

.total-weight-indicator.valid {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.total-weight-indicator.invalid {
  border-color: #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.weight-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.weight-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

/* Main Content */
.builder-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  padding: 2rem;
  flex: 1;
  overflow: hidden;
}

/* Config Panel */
.config-panel {
  overflow-y: auto;
  padding-right: 1rem;
}

.preset-section {
  margin-bottom: 2rem;
}

.preset-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.preset-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.preset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preset-card:hover {
  border-color: #5521be;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(85, 33, 190, 0.15);
}

.preset-card:hover::before,
.preset-card.selected::before {
  opacity: 1;
}

.preset-card.selected {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.05) 0%, rgba(224, 54, 175, 0.02) 100%);
}

.preset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.preset-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.preset-tag {
  font-size: 0.7rem !important;
  padding: 0.25rem 0.5rem !important;
}

.preset-description {
  color: #64748b;
  font-size: 0.85rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.preset-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.preset-metric-chip {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid;
  font-weight: 500;
}

.more-metrics {
  font-size: 0.7rem;
  color: #64748b;
  font-style: italic;
}

/* Metrics Section */
.metrics-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-left: 4px solid;
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.metric-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.metric-color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.metric-description {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.3;
}

.metric-weight-display {
  text-align: right;
}

.weight-percentage {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
}

.metric-controls {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: center;
}

.slider-container {
  flex: 1;
}

.weight-input {
  width: 80px;
}

/* Custom Slider Styling */
:host ::ng-deep .custom-slider .p-slider {
  background: #e2e8f0;
  height: 6px;
  border-radius: 3px;
}

:host ::ng-deep .custom-slider .p-slider .p-slider-range {
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
  border-radius: 3px;
}

:host ::ng-deep .custom-slider .p-slider .p-slider-handle {
  width: 18px;
  height: 18px;
  background: white;
  border: 3px solid #5521be;
  border-radius: 50%;
  transition: all 0.3s ease;
}

:host ::ng-deep .custom-slider .p-slider .p-slider-handle:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.3);
}

/* Weight Input Styling */
:host ::ng-deep .weight-input-field input {
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Preview Panel */
.preview-panel {
  background: linear-gradient(135deg, rgba(254, 236, 249, 0.3) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(85, 33, 190, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  overflow-y: auto;
  height: fit-content;
  max-height: 100%;
}

.preview-header {
  margin-bottom: 1.5rem;
}

.preview-header h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.preview-header p {
  margin: 0;
  color: #64748b;
  font-size: 0.85rem;
}

/* Weight Chart */
.weight-chart-section {
  margin-bottom: 2rem;
}

.weight-chart-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

.weight-chart {
  display: flex;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  background: #f1f5f9;
  margin-bottom: 1rem;
}

.weight-bar {
  height: 100%;
  transition: all 0.3s ease;
}

.weight-legend {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  color: #64748b;
}

.legend-weight {
  font-weight: 600;
  color: #1e293b;
}

/* Sample Calculations */
.sample-calculation {
  margin-bottom: 2rem;
}

.sample-calculation h4 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

.calculation-cards {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.calculation-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  font-size: 0.8rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.ad-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.85rem;
}

.score-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.old-score {
  color: #64748b;
}

.new-score {
  font-weight: 600;
}

.new-score.improved {
  color: #10b981;
}

.new-score.decreased {
  color: #ef4444;
}

.metric-breakdown {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
}

.breakdown-label {
  color: #64748b;
}

.breakdown-value {
  font-weight: 600;
  color: #1e293b;
}

/* Configuration Summary */
.config-summary h4 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.85rem;
}

.stat-label {
  color: #64748b;
}

.stat-value {
  font-weight: 600;
  color: #1e293b;
}

.stat-value.valid {
  color: #10b981;
}

/* Footer */
.builder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(85, 33, 190, 0.1);
  background: linear-gradient(135deg, rgba(254, 236, 249, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.footer-right {
  display: flex;
  gap: 0.75rem;
}

/* Save Dialog */
:host ::ng-deep .save-config-dialog .p-dialog {
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.save-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  padding: 0.5rem 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .builder-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .preview-panel {
    order: -1;
  }
  
  .preset-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .builder-content {
    padding: 1rem;
  }
  
  .builder-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
