/* Fullscreen Dialog Styling */
:host ::ng-deep .fullscreen-score-builder .p-dialog {
  border-radius: 0;
  box-shadow: none;
  border: none;
  overflow: hidden;
  margin: 0;
}

:host ::ng-deep .fullscreen-score-builder .p-dialog-header {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  font-family: 'Poppins', sans-serif;
}

:host ::ng-deep .fullscreen-score-builder .p-dialog-content {
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  height: calc(100vh - 80px);
  overflow-y: auto;
}

:host ::ng-deep .fullscreen-score-builder .p-dialog-header-close {
  color: white;
  width: 2rem;
  height: 2rem;
}

:host ::ng-deep .fullscreen-score-builder .p-dialog-header-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Fullscreen Header */
.fullscreen-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.header-title h2 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
}

.header-title p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
}

/* Main Container */
.fullscreen-content {
  display: flex;
  flex-direction: column;
  font-family: 'Poppins', sans-serif;
  gap: 2rem;
  padding: 2rem;
  height: 100%;
}

/* Templates Section */
.templates-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(85, 33, 190, 0.1);
}

.templates-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.templates-section h3 i {
  color: #5521be;
}

.template-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.template-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover {
  border-color: #5521be;
  transform: translateY(-4px);
  box-shadow: 0 12px 25px rgba(85, 33, 190, 0.15);
}

.template-card:hover::before,
.template-card.selected::before {
  opacity: 1;
}

.template-card.selected {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.05) 0%, rgba(224, 54, 175, 0.05) 100%);
  box-shadow: 0 8px 25px rgba(85, 33, 190, 0.2);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.template-header h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
}

.template-description {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.template-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.metric-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid;
  font-size: 0.8rem;
  font-weight: 600;
}

.metric-name {
  color: #374151;
}

.metric-weight {
  color: #1e293b;
  font-weight: 700;
}

.more-metrics {
  font-size: 0.8rem;
  color: #6b7280;
  font-style: italic;
}

/* Template Subsections */
.template-subsection {
  margin-bottom: 2rem;
}

.template-subsection:last-child {
  margin-bottom: 0;
}

.subsection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.subsection-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.subsection-header h3 i {
  color: #5521be;
}

/* Custom Template Styling */
.custom-template {
  border-left: 4px solid #5521be;
}

.custom-template .template-header {
  align-items: flex-start;
}

.custom-actions {
  display: flex;
  gap: 0.25rem;
}

.custom-actions .p-button {
  width: 2rem;
  height: 2rem;
}

/* Main Grid */
.main-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  flex: 1;
  min-height: 0;
}

/* Custom Section */
.custom-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(85, 33, 190, 0.1);
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.section-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-header h3 i {
  color: #5521be;
}

.weight-indicator {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 120px;
}

.weight-indicator.valid {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.weight-indicator.invalid {
  border-color: #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.weight-label {
  display: block;
  font-size: 0.7rem;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.weight-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 0.75rem;
}

.metric-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.8) 100%);
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.metric-card.active {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.metric-card.inactive {
  opacity: 0.6;
  border-style: dashed;
}

.metric-card.inactive:hover {
  opacity: 1;
  border-color: #5521be;
  border-style: solid;
}

.metric-info {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.metric-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.metric-details {
  flex: 1;
}

.metric-details h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 700;
  color: #1e293b;
}

.metric-details p {
  margin: 0;
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.3;
}

.metric-control {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.weight-display {
  text-align: center;
  margin-bottom: 0.5rem;
}

.weight-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
}

.weight-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.weight-adjuster {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  justify-content: center;
}

.weight-btn {
  width: 28px;
  height: 28px;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.weight-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #5521be;
  color: #5521be;
}

.weight-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.weight-btn-minus:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #ef4444;
  color: #ef4444;
}

.weight-btn-plus:hover:not(:disabled) {
  background: #f0fdf4;
  border-color: #10b981;
  color: #10b981;
}

.weight-input-container {
  flex: 1;
  max-width: 60px;
}

.delete-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  background: #fef2f2;
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  align-self: center;
}

.delete-btn:hover {
  background: #fee2e2;
  border-color: #ef4444;
  transform: scale(1.05);
}

.metric-add {
  text-align: center;
  padding: 1rem 0;
}

.add-metric-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  background: transparent;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
}

.add-metric-btn:hover {
  border-color: #10b981;
  background: #f0fdf4;
  color: #10b981;
  transform: translateY(-1px);
}

.add-metric-btn i {
  font-size: 0.9rem;
}

/* Compact Weight Input Styling */
:host ::ng-deep .compact-weight-input .p-inputnumber {
  width: 100%;
}

:host ::ng-deep .compact-weight-input .p-inputnumber-input {
  width: 100%;
  height: 28px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
}

:host ::ng-deep .compact-weight-input .p-inputnumber-input:focus {
  border-color: #5521be;
  box-shadow: 0 0 0 1px #5521be;
}

/* Metrics Section */
.metrics-section {
  background: white;
  border: 1px solid rgba(85, 33, 190, 0.1);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.metric-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.metric-add {
  padding: 1rem;
  text-align: center;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 0.5rem;
  margin-top: 0.75rem;
}

/* Preview Section */
.preview-section {
  background: linear-gradient(135deg, rgba(254, 236, 249, 0.3) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(85, 33, 190, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  overflow-y: auto;
}

.preview-subtitle {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  color: #64748b;
}

.preview-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.preview-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.preview-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.ad-info {
  flex: 1;
}

.ad-name {
  font-weight: 700;
  color: #1e293b;
  font-size: 0.95rem;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.ad-type {
  font-size: 0.75rem;
  color: #6b7280;
  background: #f3f4f6;
  padding: 0.2rem 0.5rem;
  border-radius: 0.375rem;
}

.score-comparison {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
}

.score-item {
  text-align: center;
  min-width: 60px;
}

.score-label {
  display: block;
  font-size: 0.7rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.score-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
}

.score-item.new.improved .score-value {
  color: #10b981;
}

.score-item.new.decreased .score-value {
  color: #ef4444;
}

.score-arrow {
  color: #9ca3af;
  font-size: 0.8rem;
}

.metrics-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.breakdown-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metric-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-name {
  color: #374151;
  font-weight: 500;
}

.metric-weight {
  color: #6b7280;
  font-size: 0.75rem;
}

.metric-value {
  font-weight: 600;
  color: #1e293b;
}

.no-preview {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-preview i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Main Content */
.builder-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  padding: 2rem;
  flex: 1;
  overflow: hidden;
}

/* Config Panel */
.config-panel {
  overflow-y: auto;
  padding-right: 1rem;
}

.preset-section {
  margin-bottom: 2rem;
}

.preset-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.preset-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.preset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preset-card:hover {
  border-color: #5521be;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(85, 33, 190, 0.15);
}

.preset-card:hover::before,
.preset-card.selected::before {
  opacity: 1;
}

.preset-card.selected {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.05) 0%, rgba(224, 54, 175, 0.02) 100%);
}

.preset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.preset-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.preset-tag {
  font-size: 0.7rem !important;
  padding: 0.25rem 0.5rem !important;
}

.preset-description {
  color: #64748b;
  font-size: 0.85rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.preset-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.preset-metric-chip {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid;
  font-weight: 500;
}

.more-metrics {
  font-size: 0.7rem;
  color: #64748b;
  font-style: italic;
}

/* Metrics Section */
.metrics-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-left: 4px solid;
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.metric-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.metric-color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.metric-description {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.3;
}

.metric-weight-display {
  text-align: right;
}

.weight-percentage {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
}

.metric-controls {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: center;
}

.slider-container {
  flex: 1;
}

.weight-input {
  width: 80px;
}

/* Custom Slider Styling */
:host ::ng-deep .custom-slider .p-slider {
  background: #e2e8f0;
  height: 6px;
  border-radius: 3px;
}

:host ::ng-deep .custom-slider .p-slider .p-slider-range {
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
  border-radius: 3px;
}

:host ::ng-deep .custom-slider .p-slider .p-slider-handle {
  width: 18px;
  height: 18px;
  background: white;
  border: 3px solid #5521be;
  border-radius: 50%;
  transition: all 0.3s ease;
}

:host ::ng-deep .custom-slider .p-slider .p-slider-handle:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.3);
}

/* Weight Input Styling */
:host ::ng-deep .weight-input-field input {
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Preview Panel */
.preview-panel {
  background: linear-gradient(135deg, rgba(254, 236, 249, 0.3) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(85, 33, 190, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  overflow-y: auto;
  height: fit-content;
  max-height: 100%;
}

.preview-header {
  margin-bottom: 1.5rem;
}

.preview-header h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.preview-header p {
  margin: 0;
  color: #64748b;
  font-size: 0.85rem;
}

/* Weight Chart */
.weight-chart-section {
  margin-bottom: 2rem;
}

.weight-chart-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

.weight-chart {
  display: flex;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  background: #f1f5f9;
  margin-bottom: 1rem;
}

.weight-bar {
  height: 100%;
  transition: all 0.3s ease;
}

.weight-legend {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  color: #64748b;
}

.legend-weight {
  font-weight: 600;
  color: #1e293b;
}

/* Sample Calculations */
.sample-calculation {
  margin-bottom: 2rem;
}

.sample-calculation h4 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

.calculation-cards {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.calculation-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  font-size: 0.8rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.ad-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.85rem;
}

.score-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.old-score {
  color: #64748b;
}

.new-score {
  font-weight: 600;
}

.new-score.improved {
  color: #10b981;
}

.new-score.decreased {
  color: #ef4444;
}

.metric-breakdown {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
}

.breakdown-label {
  color: #64748b;
}

.breakdown-value {
  font-weight: 600;
  color: #1e293b;
}

/* Configuration Summary */
.config-summary h4 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.85rem;
}

.stat-label {
  color: #64748b;
}

.stat-value {
  font-weight: 600;
  color: #1e293b;
}

.stat-value.valid {
  color: #10b981;
}

/* Fullscreen Footer */
.fullscreen-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: white;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

.footer-left {
  flex: 1;
}

.validation-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.validation-status.valid {
  color: #10b981;
}

.validation-status.invalid {
  color: #ef4444;
}

.footer-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* Save Dialog */
:host ::ng-deep .save-config-dialog .p-dialog {
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.save-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  padding: 0.5rem 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .preview-section {
    order: -1;
  }

  .template-cards {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .fullscreen-content {
    padding: 1rem;
    gap: 1.5rem;
  }

  .fullscreen-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .header-title h2 {
    font-size: 1.5rem;
  }

  .template-cards {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .fullscreen-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .footer-actions {
    justify-content: center;
  }

  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .score-comparison {
    align-self: flex-end;
  }
}
