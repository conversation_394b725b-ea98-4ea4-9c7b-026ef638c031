import { Injectable } from '@angular/core';
import { catchError, combineLatest, from, map, Observable, of } from 'rxjs';
import { SupabaseService } from '../../../core/services';
import {
  AdWinner,
  AdWinnerFilters,
  AdWinnerMetric,
  AdWinnerResponse,
  FilterOption,
  WeeklyPeriod,
  PerformanceScoreConfiguration,
  PerformanceScoreMetric,
} from '../models';
import { PerformanceScoreConfigService } from './performance-score-config.service';

@Injectable({
  providedIn: 'root',
})
export class AdWinnerService {
  constructor(
    private supabaseService: SupabaseService,
    private configService: PerformanceScoreConfigService
  ) {}

  /**
   * Get all available weekly periods
   */
  getWeeklyPeriods(): Observable<WeeklyPeriod[]> {
    return from(
      this.supabaseService.client
        .from('view_ad_insights_weekly_periods')
        .select('*')
        .order('week_start', { ascending: false }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching weekly periods:', response.error);
          return [];
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching weekly periods:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get all accounts for filter dropdown
   */
  getAccounts(): Observable<FilterOption[]> {
    return from(
      this.supabaseService.client
        .from('accounts')
        .select('id, name')
        .order('name'),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching accounts:', response.error);
          return [];
        }
        return (response.data || []).map((account) => ({
          label: account.name,
          value: account.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching accounts:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get campaigns for specific account
   */
  getCampaigns(accountId?: string): Observable<FilterOption[]> {
    let query = this.supabaseService.client
      .from('campaigns')
      .select('id, name');

    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    return from(query.order('name')).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching campaigns:', response.error);
          return [];
        }
        return (response.data || []).map((campaign) => ({
          label: campaign.name,
          value: campaign.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching campaigns:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get ad sets for specific campaign
   */
  getAdSets(campaignId?: string): Observable<FilterOption[]> {
    let query = this.supabaseService.client.from('adsets').select('id, name');

    if (campaignId) {
      query = query.eq('campaign_id', campaignId);
    }

    return from(query.order('name')).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching adsets:', response.error);
          return [];
        }
        return (response.data || []).map((adset) => ({
          label: adset.name,
          value: adset.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching adsets:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get ad winners based on filters
   */
  getAdWinners(filters: AdWinnerFilters = {}): Observable<AdWinnerResponse> {
    return combineLatest([
      this.getAdInsightsWithDetails(filters),
      this.getWeeklyPeriods(),
    ]).pipe(
      map(([insights, periods]) => {
        const adWinners = this.calculateAdWinners(insights);

        return {
          data: adWinners,
          total_count: adWinners.length,
          filters_applied: filters,
          periods_available: periods,
        };
      }),
      catchError((error) => {
        console.error('Error fetching ad winners:', error);
        return of({
          data: [],
          total_count: 0,
          filters_applied: filters,
          periods_available: [],
        });
      }),
    );
  }

  /**
   * Get ad insights with full details (ads, adsets, campaigns, accounts)
   */
  private getAdInsightsWithDetails(
    filters: AdWinnerFilters,
  ): Observable<any[]> {
    let query = this.supabaseService.client.from('ad_insights_weekly').select(`
        *,
        ads!inner(
          id,
          name,
          created_time,
          status,
          video_id,
          creative,
          videos(
            id,
            source
          ),
          adsets!inner(
            id,
            name,
            status,
            campaigns!inner(
              id,
              name,
              status,
              objective,
              accounts!inner(
                id,
                name,
                status
              )
            )
          )
        )
      `);

    // Apply filters
    if (filters.account_id) {
      query = query.eq('account_id', filters.account_id);
    }

    if (filters.campaign_id) {
      query = query.eq('campaign_id', filters.campaign_id);
    }

    if (filters.adset_id) {
      query = query.eq('adset_id', filters.adset_id);
    }

    if (filters.week_start) {
      query = query.eq('week_start', filters.week_start);
    }

    if (filters.week_end) {
      query = query.eq('week_end', filters.week_end);
    }

    if (filters.min_spend) {
      query = query.gte('total_spend', filters.min_spend);
    }

    if (filters.min_impressions) {
      query = query.gte('total_impressions', filters.min_impressions);
    }

    return from(query.order('total_spend', { ascending: false })).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching ad insights:', response.error);
          return [];
        }
        return response.data || [];
      }),
    );
  }

  /**
   * Calculate ad winners based on performance metrics
   */
  private calculateAdWinners(
    insights: any[],
    metric: AdWinnerMetric,
  ): AdWinner[] {
    // Group insights by adset
    const adsetGroups = new Map<string, any[]>();

    insights.forEach((insight) => {
      const adsetId = insight.adset_id;
      if (!adsetGroups.has(adsetId)) {
        adsetGroups.set(adsetId, []);
      }
      adsetGroups.get(adsetId)!.push(insight);
    });

    const winners: AdWinner[] = [];

    // Find winner in each adset
    adsetGroups.forEach((adsetInsights, adsetId) => {
      // Sort by the selected metric (descending for most metrics, ascending for costs)
      const isAscending = [
        'cpa',
        'avg_cpm',
        'cost_per_landing_page_view',
        'cost_per_add_to_cart',
      ].includes(metric);

      const sortedAds = adsetInsights.sort((a, b) => {
        const aValue = this.getMetricValue(a, metric);
        const bValue = this.getMetricValue(b, metric);

        if (aValue === null && bValue === null) return 0;
        if (aValue === null) return 1;
        if (bValue === null) return -1;

        return isAscending ? aValue - bValue : bValue - aValue;
      });

      // Create winners with ranking
      sortedAds.forEach((insight, index) => {
        const metricValue = this.getMetricValue(insight, metric);
        if (metricValue !== null) {
          const winner: AdWinner = {
            id: insight.ads.id,
            adset_id: insight.adset_id,
            campaign_id: insight.campaign_id,
            account_id: insight.account_id,
            name: insight.ads.name,
            created_time: insight.ads.created_time,
            status: insight.ads.status,
            video_id: insight.ads.video_id,
            video: insight.ads.videos,
            creative: insight.ads.creative || {},
            created_at: insight.ads.created_at,
            updated_at: insight.ads.updated_at,
            adset: insight.ads.adsets,
            campaign: insight.ads.adsets.campaigns,
            account: insight.ads.adsets.campaigns.accounts,
            weekly_insights: insight,
            performance_score: this.calculatePerformanceScore(insight, metric),
            rank_in_adset: index + 1,
            best_metric: metric,
            best_metric_value: metricValue,
          };

          winners.push(winner);
        }
      });
    });

    // Sort all winners by performance score
    return winners.sort((a, b) => b.performance_score - a.performance_score);
  }

  /**
   * Get metric value from insight data
   */
  private getMetricValue(insight: any, metric: AdWinnerMetric): number | null {
    const value = insight[metric];
    return value !== null && value !== undefined && !isNaN(value)
      ? Number(value)
      : null;
  }

  /**
   * Calculate overall performance score (0-100) with primary metric emphasis
   */
  private calculatePerformanceScore(
    insight: any,
    primaryMetric: AdWinnerMetric,
  ): number {
    // Base weights for all metrics (must sum to 1.0 = 100%)
    const baseWeights = {
      roas: 0.2, // 20%
      conversion_rate: 0.2, // 20%
      hook_rate: 0.12, // 12%
      hold_rate: 0.12, // 12%
      traffic_quality: 0.15, // 15%
      outbound_ctr: 0.08, // 8%
      cpa: 0.08, // 8%
      avg_cpm: 0.05, // 5%
    };

    // Give primary metric higher weight (50% of total score)
    const weights = { ...baseWeights };
    const primaryWeight = 0.5;
    const remainingWeight = 0.5;

    // Simple approach: ensure weights sum to exactly 1.0
    // Step 1: Set primary metric to 50%
    weights[primaryMetric] = primaryWeight;

    // Step 2: Get non-primary metrics and their base weights
    const nonPrimaryMetrics = Object.entries(baseWeights).filter(
      ([metric]) => metric !== primaryMetric,
    );

    // Step 3: Calculate sum of base weights for non-primary metrics
    const nonPrimarySum = nonPrimaryMetrics.reduce(
      (sum, [, weight]) => sum + weight,
      0,
    );

    // Step 4: Distribute remaining 50% proportionally
    nonPrimaryMetrics.forEach(([metric, baseWeight]) => {
      weights[metric as AdWinnerMetric] =
        (baseWeight / nonPrimarySum) * remainingWeight;
    });

    // Step 5: Ensure exact 100% by adjusting largest non-primary metric
    const currentTotalWeight = Object.values(weights).reduce(
      (sum, w) => sum + w,
      0,
    );
    const adjustment = 1.0 - currentTotalWeight;

    if (Math.abs(adjustment) > 0.0001) {
      // Find largest non-primary metric and adjust it
      const largestNonPrimary = nonPrimaryMetrics.sort(
        ([metricA], [metricB]) =>
          weights[metricB as AdWinnerMetric] -
          weights[metricA as AdWinnerMetric],
      )[0];
      if (largestNonPrimary) {
        weights[largestNonPrimary[0] as AdWinnerMetric] += adjustment;
      }
    }

    let score = 0;
    let totalWeight = 0;

    Object.entries(weights).forEach(([metric, weight]) => {
      const value = this.getMetricValue(insight, metric as AdWinnerMetric);
      if (value !== null) {
        let normalizedValue = 0;

        switch (metric) {
          case 'roas':
            normalizedValue = Math.min(value * 10, 100); // ROAS of 10+ = 100 points
            break;
          case 'conversion_rate':
          case 'hook_rate':
          case 'hold_rate':
          case 'traffic_quality':
          case 'outbound_ctr':
            normalizedValue = Math.min(value * 100, 100); // Already in percentage
            break;
          case 'cpa':
            // Lower CPA is better, normalize inversely (assuming max CPA of $100)
            normalizedValue = Math.max(0, 100 - (value / 100) * 100);
            break;
          case 'avg_cpm':
            // Lower CPM is better, normalize inversely (assuming max CPM of $50)
            normalizedValue = Math.max(0, 100 - (value / 50) * 100);
            break;
        }

        score += normalizedValue * weight;
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? Math.round((score / totalWeight) * 100) / 100 : 0;
  }

  /**
   * Calculate performance score using custom configuration
   */
  calculatePerformanceScoreWithConfig(
    insight: any,
    config: PerformanceScoreConfiguration
  ): number {
    let score = 0;
    let totalWeight = 0;

    config.metrics_config.metrics.forEach(metric => {
      if (metric.weight > 0) {
        const value = this.getMetricValue(insight, metric.metric);
        if (value !== null) {
          let normalizedValue = 0;

          switch (metric.metric) {
            case 'roas':
              normalizedValue = Math.min(value * 10, 100);
              break;
            case 'conversion_rate':
            case 'hook_rate':
            case 'hold_rate':
            case 'traffic_quality':
            case 'outbound_ctr':
              normalizedValue = Math.min(value * 100, 100);
              break;
            case 'cpa':
            case 'cost_per_landing_page_view':
            case 'cost_per_add_to_cart':
              normalizedValue = Math.max(0, 100 - (value / 100) * 100);
              break;
            case 'avg_cpm':
              normalizedValue = Math.max(0, 100 - (value / 50) * 100);
              break;
            case 'total_spend':
            case 'total_impressions':
            case 'total_reach':
            case 'total_purchases':
            case 'total_purchase_value':
            case 'total_landing_page_views':
            case 'total_outbound_clicks':
            case 'total_video_plays_3s':
            case 'total_add_to_carts':
            case 'unique_outbound_clicks':
              // For volume metrics, normalize based on percentile ranking
              normalizedValue = Math.min((value / 10000) * 100, 100);
              break;
            case 'avg_frequency':
              normalizedValue = Math.min(value * 20, 100);
              break;
            case 'average_order_value':
              normalizedValue = Math.min((value / 200) * 100, 100);
              break;
          }

          score += normalizedValue * (metric.weight / 100);
          totalWeight += metric.weight;
        }
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }
}
