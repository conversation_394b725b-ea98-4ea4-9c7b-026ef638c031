/* Ad Winners Component Styles */
.ad-winners-container {
  padding: 1rem 2rem;
  width: 100%;

  margin: 0 auto;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  overflow-x: auto;
}

/* Header Section */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  text-align: center;
  padding: 2rem 0;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.page-title i {
  color: #e036af;
  -webkit-text-fill-color: #e036af;
}

.page-description {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* Filters Section */
.filters-section {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto 2rem;
}

.filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.filter-actions {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Metric Option Styling */
.metric-option {
  padding: 0.5rem 0;
}

.metric-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.metric-description {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.4;
}

/* Results Section */
.results-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto 1rem;
}

.stat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Performance Config Card */
.performance-config-card {
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.05) 0%, rgba(224, 54, 175, 0.05) 100%);
  border: 2px solid rgba(85, 33, 190, 0.15);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.performance-config-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
}

.performance-config-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(85, 33, 190, 0.2);
  border-color: rgba(85, 33, 190, 0.3);
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.08) 0%, rgba(224, 54, 175, 0.08) 100%);
}

.config-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.config-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.config-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.config-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.config-metrics {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.primary-metric {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  font-size: 0.7rem;
}

.metrics-count {
  color: #6b7280;
  font-weight: 500;
}

.config-edit {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(85, 33, 190, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5521be;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.performance-config-card:hover .config-edit {
  background: rgba(85, 33, 190, 0.2);
  transform: scale(1.1);
}

/* Table Card */
.table-card {
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem 0 1.5rem 0;
  border-bottom: none;
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(254, 236, 249, 0.3) 100%);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(85, 33, 190, 0.08);
  border: 1px solid rgba(85, 33, 190, 0.1);
}

.table-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  margin-left: 3rem;
}

.table-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.view-toggle {
  display: flex;
  align-items: center;
  border-radius: 12px;
  padding: 0.25rem;
  transition: all 0.3s ease;

  p-button {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.view-toggle:hover {
  transform: translateY(-1px);
  border-color: rgba(85, 33, 190, 0.25);
}

.global-filter {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.1) 100%);
  border: 1px solid rgba(85, 33, 190, 0.15);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  padding: 0.6rem 1rem 0.6rem 2.25rem;
  font-size: 0.875rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.08);
  min-width: 220px;
}

.global-filter:hover {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 236, 249, 0.2) 100%);
  box-shadow: 0 4px 16px rgba(85, 33, 190, 0.12);
  transform: translateY(-1px);
}

.global-filter:focus {
  outline: none;
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 236, 249, 0.15) 100%);
  box-shadow: 0 0 0 3px rgba(85, 33, 190, 0.15), 0 4px 16px rgba(85, 33, 190, 0.12);
  transform: translateY(-1px);
}

.global-filter::placeholder {
  color: #9ca3af;
  font-style: italic;
  font-weight: 400;
}

/* Search Icon Styling */
.p-input-icon-left > i {
  color: #9ca3af;
  left: 0.75rem;
  transition: color 0.3s ease;
  font-size: 0.875rem;
}

.p-input-icon-left:hover > i,
.p-input-icon-left:focus-within > i {
  color: #5521be;
}

/* Table Specific Styles */
::ng-deep .p-datatable {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  min-width: 1200px;
}

::ng-deep .p-datatable .p-datatable-wrapper {
  overflow-x: auto;
}

::ng-deep .p-datatable .p-datatable-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: none;
  border-bottom: 2px solid #e2e8f0;
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 0.75rem;
}

::ng-deep .p-datatable .p-datatable-tbody > tr {
  transition: background-color 0.2s ease;
}

::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background-color: rgba(85, 33, 190, 0.05) !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  border: none;
  border-bottom: 1px solid #f1f5f9;
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

/* Creative Cell */
.creative-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.creative-wrapper {
  position: relative;
  display: inline-block;
}

.ad-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.ad-thumbnail:hover {
  transform: scale(1.05);
}

.ad-thumbnail.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.ad-thumbnail.clickable:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.3);
}

/* Video Thumbnail Styles */
.video-thumbnail-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.video-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  pointer-events: none;
}

.video-thumbnail-wrapper:hover .video-play-overlay {
  background: rgba(85, 33, 190, 0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.video-thumbnail-wrapper:hover .video-thumbnail {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.3);
}

.no-image-placeholder {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1.25rem;
}

/* Creative Type Badge */
.creative-type-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 2px solid white;
  z-index: 10;
}

.creative-type-badge.card-type-badge {
  width: 24px;
  height: 24px;
  font-size: 0.875rem;
  bottom: -3px;
  right: -3px;
}

.creative-type-badge[data-type="video"] {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.creative-type-badge[data-type="photo"] {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.creative-type-badge[data-type="link"] {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.creative-type-badge[data-type="template"] {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.creative-type-badge[data-type="unknown"] {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

/* Ad Name Cell */
.ad-name-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ad-name {
  font-weight: 600;
  color: #374151;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  align-self: flex-start;
}

/* Score Cell - Enhanced */
.score-cell {
  min-width: 140px;
  position: relative;
  padding: 0.75rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(254, 236, 249, 0.3) 100%);
  border-radius: 12px;
  border: 1px solid rgba(85, 33, 190, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.08);
  transition: all 0.3s ease;
}

.score-cell:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(85, 33, 190, 0.15);
  border-color: rgba(85, 33, 190, 0.2);
}

.score-value {
  font-weight: 700;
  font-size: 1.1rem;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  display: block;
  margin-top: 0.5rem;
  position: relative;
}

.score-value::before {
  content: '';
  position: absolute;
  top: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  border-radius: 1px;
}

/* Metric Values */
.metric-value {
  font-weight: 600;
  color: #374151;
  font-variant-numeric: tabular-nums;
}

/* Rank Display - Clean without background */
.rank-tag {
  font-weight: 700;
  font-size: 2rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  transition: all 0.3s ease;
}

.rank-tag:hover {
  transform: scale(1.1);
}

/* Rank Icons */
.rank-tag::before {
  font-size: 1.2rem;
  font-weight: 900;
}

/* Gold Medal for #1 */
::ng-deep .p-tag.p-tag-success,
::ng-deep .rank-tag.p-tag-success {
  background: none !important;
  border: none !important;
  color: #fbbf24 !important;
  box-shadow: none !important;
}

::ng-deep .p-tag.p-tag-success .rank-tag::before,
::ng-deep .rank-tag.p-tag-success::before {
  content: '🥇';
}

/* Silver Medal for #2-3 */
::ng-deep .p-tag.p-tag-warning,
::ng-deep .rank-tag.p-tag-warning {
  background: none !important;
  border: none !important;
  color: #9ca3af !important;
  box-shadow: none !important;
}

::ng-deep .p-tag.p-tag-warning .rank-tag::before,
::ng-deep .rank-tag.p-tag-warning::before {
  content: '🥈';
}

/* Bronze Medal for others */
::ng-deep .p-tag.p-tag-secondary,
::ng-deep .rank-tag.p-tag-secondary {
  background: none !important;
  border: none !important;
  color: #cd7f32 !important;
  box-shadow: none !important;
}

::ng-deep .p-tag.p-tag-secondary .rank-tag::before,
::ng-deep .rank-tag.p-tag-secondary::before {
  content: '🥉';
}

/* Loading State */
.loading-row {
  padding: 2rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* Progress Bar Customization */
::ng-deep .p-progressbar {
  border-radius: 8px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  border: none;
}

::ng-deep .p-progressbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

::ng-deep .p-progressbar .p-progressbar-value {
  border-radius: 8px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: none;
}

::ng-deep .p-progressbar .p-progressbar-value::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
  border-radius: 8px 8px 0 0;
}

/* Hide progress bar label/text */
::ng-deep .p-progressbar .p-progressbar-label {
  display: none !important;
}

/* Remove any text content */
::ng-deep .p-progressbar .p-progressbar-value .p-progressbar-label {
  display: none !important;
}

::ng-deep .p-progressbar.p-progressbar-success .p-progressbar-value {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  border: none;
}

::ng-deep .p-progressbar.p-progressbar-warning .p-progressbar-value {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  border: none;
}

::ng-deep .p-progressbar.p-progressbar-danger .p-progressbar-value {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  border: none;
}

/* Tag Customization */
::ng-deep .p-tag {
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
}

::ng-deep .p-tag.p-tag-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

::ng-deep .p-tag.p-tag-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

::ng-deep .p-tag.p-tag-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

/* Button Customization */
::ng-deep .p-button {
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

::ng-deep .p-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

::ng-deep .p-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(85, 33, 190, 0.2);
}

::ng-deep .p-button:hover::before {
  left: 100%;
}

/* Primary button styling */
::ng-deep .p-button:not(.p-button-outlined):not(.p-button-text) {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  border: none;
  color: white;
}

::ng-deep .p-button:not(.p-button-outlined):not(.p-button-text):hover {
  background: linear-gradient(135deg, #4a1ca8 0%, #c026d3 100%);
}

/* Outlined button styling */
::ng-deep .p-button.p-button-outlined {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(85, 33, 190, 0.2);
  color: #5521be;
}

::ng-deep .p-button.p-button-outlined:hover {
  background: rgba(85, 33, 190, 0.05);
  border-color: rgba(85, 33, 190, 0.4);
}


/* Input Number Customization */
::ng-deep .p-inputnumber {
  width: 100%;
}

::ng-deep .p-inputnumber input {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.1) 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 500;
  color: #374151;
  padding: 0.75rem 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

::ng-deep .p-inputnumber input:hover {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 236, 249, 0.2) 100%);
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.1);
  transform: translateY(-1px);
}

::ng-deep .p-inputnumber input:focus {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 236, 249, 0.15) 100%);
  box-shadow: 0 0 0 3px rgba(85, 33, 190, 0.15), 0 4px 12px rgba(85, 33, 190, 0.1);
  transform: translateY(-1px);
  outline: none;
}

::ng-deep .p-inputnumber input::placeholder {
  color: #9ca3af;
}

/* Input Number Buttons */
::ng-deep .p-inputnumber .p-inputnumber-button {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

::ng-deep .p-inputnumber .p-inputnumber-button:hover {
  background: linear-gradient(135deg, #4a1ca8 0%, #c026d3 100%);
  transform: scale(1.05);
}

::ng-deep .p-inputnumber .p-inputnumber-button-up {
  border-radius: 0 12px 0 0;
}

::ng-deep .p-inputnumber .p-inputnumber-button-down {
  border-radius: 0 0 12px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ad-winners-container {
    padding: 0.5rem;
    min-width: auto;
    max-width: none;
    overflow-x: auto;
  }

  .page-title {
    font-size: 2rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .ad-name {
    max-width: 150px;
  }

  ::ng-deep .p-datatable {
    min-width: 800px;
  }
}

/* Image Modal Styles */
::ng-deep .image-modal .p-dialog-header {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 1.5rem;
}

::ng-deep .image-modal .p-dialog-content {
  padding: 0;
  background: #f8fafc;
  border-radius: 0;
}

::ng-deep .image-modal .p-dialog-footer {
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

::ng-deep .image-modal .p-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-title i {
  font-size: 1.125rem;
}

.modal-subtitle {
  font-size: 0.875rem;
  opacity: 0.9;
  font-weight: 400;
}

.modal-media-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background: #f8fafc;
  min-height: 300px;
}

.modal-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-video {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  background: #000;
}

.modal-footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

/* Modal Responsive */
@media (max-width: 768px) {
  ::ng-deep .image-modal .p-dialog {
    margin: 1rem;
    width: calc(100vw - 2rem) !important;
    max-width: none !important;
  }

  .modal-media-container {
    padding: 1rem;
  }

  .modal-image {
    max-height: 60vh;
  }

  .modal-video {
    max-height: 60vh;
  }

  .modal-footer-content {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-footer-content p-button {
    width: 100%;
  }
}

/* Cards View Styles */
.cards-container {
  width: 100%;
  max-width: 100%;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.ad-winner-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.ad-winner-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 25px -5px rgba(0, 0, 0, 0.15), 0 8px 10px -5px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 0;
  border-bottom: none;
}

.card-creative-section {
  width: 100%;
  height: 240px;
  position: relative;
  overflow: hidden;
  border-radius: 16px 16px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-info-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.creative-section {
  flex-shrink: 0;
}

/* Large Creative Styles for Cards */
.card-creative-large {
  width: 100%;
  height: 240px;
  object-fit: cover;
  border-radius: 0;
  transition: transform 0.3s ease;
  cursor: pointer;
  display: block;
}

.card-creative-large:hover {
  transform: scale(1.02);
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 240px;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-display {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-play-overlay-large {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(10px);
}

.video-wrapper:hover .video-play-overlay-large {
  background: rgba(85, 33, 190, 0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.video-wrapper:hover .video-display {
  transform: scale(1.05);
}

.card-creative-placeholder-large {
  width: 100%;
  height: 240px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 3rem;
}

.creative-type-badge.card-type-badge-large {
  width: 32px;
  height: 32px;
  font-size: 1rem;
  bottom: 10px;
  right: 10px;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.card-creative {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.card-creative:hover {
  transform: scale(1.05);
}

.card-creative.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-creative.clickable:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(85, 33, 190, 0.3);
}

/* Card Video Styles */
.card-creative.video-thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.cards-container .video-thumbnail-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.cards-container .video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.3s ease;
  pointer-events: none;
}

.cards-container .video-thumbnail-wrapper:hover .video-play-overlay {
  background: rgba(85, 33, 190, 0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.cards-container .video-thumbnail-wrapper:hover .video-thumbnail {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(85, 33, 190, 0.3);
}

.card-creative-placeholder {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1.5rem;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-info .ad-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: none;
}

.ad-hierarchy {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.separator {
  font-size: 0.75rem;
  color: #d1d5db;
}

.account-name, .campaign-name, .adset-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-rank {
  flex-shrink: 0;
}

.rank-badge {
  font-size: 1rem;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  transition: all 0.3s ease;
}

.rank-badge:hover {
  transform: scale(1.1);
}

/* Card rank badges with icons */
.rank-badge::before {
  font-size: 1.5rem;
  font-weight: 900;
}

.card-body {
  padding: 1.5rem;
  flex: 1;
}

.performance-section {
  margin-bottom: 1.5rem;
}

.score-display {
  text-align: center;
  position: relative;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.4) 100%);
  border-radius: 20px;
  border: 2px solid rgba(85, 33, 190, 0.1);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(85, 33, 190, 0.1);
  transition: all 0.3s ease;
}

.score-display:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(85, 33, 190, 0.2);
  border-color: rgba(85, 33, 190, 0.2);
}

.score-display::before {
  content: '⭐';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(85, 33, 190, 0.3);
}

.score-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.score-value-large {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  text-shadow: 0 2px 4px rgba(85, 33, 190, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric-item {
  text-align: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.metric-item .metric-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-item .metric-value {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  font-variant-numeric: tabular-nums;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(248, 250, 252, 0.8);
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.status-badge {
  font-size: 0.75rem;
  font-weight: 600;
}

.best-metric {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.loading-card {
  padding: 1.5rem;
}

/* Cards Responsive */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .card-info .ad-name {
    text-align: center;
  }

  .ad-hierarchy {
    justify-content: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

.p-input-icon-left {
  position: relative;
  margin-right: 2rem;

  i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 1rem;
    z-index: 1;
  }
}
