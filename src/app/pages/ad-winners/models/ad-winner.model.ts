// Base database models
export interface Account {
  id: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Campaign {
  id: string;
  account_id: string;
  name: string;
  status: string;
  objective: string;
  created_at: string;
  updated_at: string;
}

export interface AdSet {
  id: string;
  campaign_id: string;
  account_id: string;
  name: string;
  status: string;
  targeting: any;
  budget_remaining: number;
  daily_budget: number;
  lifetime_budget: number;
  created_at: string;
  updated_at: string;
}

export interface Video {
  id: string;
  source?: string;
  created_at: string;
  updated_at: string;
}

export interface Ad {
  id: string;
  adset_id: string;
  campaign_id: string;
  account_id: string;
  name: string;
  created_time: string;
  status: string;
  video_id?: string;
  video?: Video;
  creative: AdCreative;
  created_at: string;
  updated_at: string;
}

export interface AdCreative {
  image_url?: string;
  video_url?: string;
  title?: string;
  body?: string;
  call_to_action?: string;
  link_url?: string;
  thumbnail_url?: string;
  object_story_spec?: {
    page_id?: string;
    instagram_actor_id?: string;
    video_data?: {
      video_id?: string;
      image_url?: string;
      video_url?: string;
      call_to_action?: any;
      message?: string;
      title?: string;
      description?: string;
    };
    photo_data?: {
      image_url?: string;
      call_to_action?: any;
      message?: string;
      url?: string;
    };
    link_data?: {
      image_url?: string;
      video_id?: string;
      call_to_action?: any;
      description?: string;
      link?: string;
      message?: string;
      name?: string;
      picture?: string;
    };
    template_data?: {
      call_to_action?: any;
      format_option?: string;
      message?: string;
      name?: string;
      template_url_spec?: any;
    };
  };
}

export interface AdInsightsWeekly {
  id: string;
  ad_id: string;
  adset_id: string;
  campaign_id: string;
  account_id: string;
  week_start: string;
  week_end: string;

  // Basic metrics
  total_impressions: number;
  total_spend: number;
  total_reach: number;
  avg_cpm: number;
  avg_frequency: number;

  // Conversion metrics
  total_purchases: number;
  total_purchase_value: number;
  cpa: number;
  roas: number;

  // Engagement metrics
  total_landing_page_views: number;
  cost_per_landing_page_view: number;
  total_outbound_clicks: number;
  unique_outbound_clicks: number;
  outbound_ctr: number;
  total_video_plays_3s: number;
  total_add_to_carts: number;
  cost_per_add_to_cart: number;

  // Advanced metrics
  hook_rate: number;
  hold_rate: number;
  conversion_rate: number;
  average_order_value: number;
  traffic_quality: number;

  created_at: string;
  created_time: string;
  updated_at: string;
}

export interface WeeklyPeriod {
  week_start: string;
  week_end: string;
}

// Extended models with relationships
export interface AdWithDetails extends Ad {
  adset?: AdSet;
  campaign?: Campaign;
  account?: Account;
  insights?: AdInsightsWeekly[];
}

export interface AdWinner extends AdWithDetails {
  performance_score: number;
  rank_in_adset: number;
  best_metric: string;
  best_metric_value: number;
  weekly_insights: AdInsightsWeekly;
  runningTimeDays?: number; // Computed property for sorting
}

// Filter and query types
export interface AdWinnerFilters {
  account_id?: string;
  campaign_id?: string;
  adset_id?: string;
  week_start?: string;
  week_end?: string;
  min_spend?: number;
  min_impressions?: number;
}

export type AdWinnerMetric =
  | 'roas'
  | 'conversion_rate'
  | 'hook_rate'
  | 'hold_rate'
  | 'traffic_quality'
  | 'cpa'
  | 'avg_cpm'
  | 'outbound_ctr'
  | 'total_spend'
  | 'total_impressions'
  | 'total_reach'
  | 'avg_frequency'
  | 'total_purchases'
  | 'total_purchase_value'
  | 'total_landing_page_views'
  | 'cost_per_landing_page_view'
  | 'total_outbound_clicks'
  | 'total_video_plays_3s'
  | 'total_add_to_carts'
  | 'cost_per_add_to_cart'
  | 'average_order_value'
  | 'unique_outbound_clicks';

export interface AdWinnerResponse {
  data: AdWinner[];
  total_count: number;
  filters_applied: AdWinnerFilters;
  periods_available: WeeklyPeriod[];
}

// UI specific types
export interface FilterOption {
  label: string;
  value: string;
}

export interface MetricOption {
  label: string;
  value: AdWinnerMetric;
  description: string;
  format: 'percentage' | 'currency' | 'number';
}

// Performance Score Configuration models
export interface PerformanceScoreMetric {
  metric: AdWinnerMetric;
  label: string;
  description: string;
  format: 'percentage' | 'currency' | 'number';
  color: string;
  weight: number;
}

export interface PerformanceScoreConfiguration {
  id?: string;
  name: string;
  description?: string;
  is_default?: boolean;
  is_system?: boolean;
  metrics_config: {
    metrics: PerformanceScoreMetric[];
  };
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PerformanceScoreConfigRequest {
  name: string;
  description?: string;
  metrics_config: {
    metrics: PerformanceScoreMetric[];
  };
}

export interface AdWinnerTableColumn {
  field: string;
  header: string;
  sortable: boolean;
  filterable: boolean;
  format?: 'currency' | 'percentage' | 'number' | 'date';
}

// Constants - All available metrics for Performance Score Builder
export const ALL_AVAILABLE_METRICS: PerformanceScoreMetric[] = [
  {
    metric: 'roas',
    label: 'ROAS',
    description: 'Return on Ad Spend',
    format: 'number',
    color: '#10b981',
    weight: 0
  },
  {
    metric: 'conversion_rate',
    label: 'Conversion Rate',
    description: 'Purchase rate (purchases / unique outbound clicks)',
    format: 'percentage',
    color: '#3b82f6',
    weight: 0
  },
  {
    metric: 'hook_rate',
    label: 'Hook Rate',
    description: 'Video view rate (3-second plays / impressions)',
    format: 'percentage',
    color: '#f59e0b',
    weight: 0
  },
  {
    metric: 'hold_rate',
    label: 'Hold Rate',
    description: 'Video retention rate (viewers who watched 100% / 3-second plays)',
    format: 'percentage',
    color: '#06b6d4',
    weight: 0
  },
  {
    metric: 'traffic_quality',
    label: 'Traffic Quality',
    description: 'Landing page view rate (landing page views / outbound clicks)',
    format: 'percentage',
    color: '#8b5cf6',
    weight: 0
  },
  {
    metric: 'cpa',
    label: 'CPA',
    description: 'Cost per acquisition',
    format: 'currency',
    color: '#ef4444',
    weight: 0
  },
  {
    metric: 'avg_cpm',
    label: 'CPM',
    description: 'Cost per thousand impressions',
    format: 'currency',
    color: '#84cc16',
    weight: 0
  },
  {
    metric: 'outbound_ctr',
    label: 'Outbound CTR',
    description: 'Click-through rate to landing page',
    format: 'percentage',
    color: '#ec4899',
    weight: 0
  },
  {
    metric: 'total_spend',
    label: 'Total Spend',
    description: 'Total amount spent on ads',
    format: 'currency',
    color: '#6366f1',
    weight: 0
  },
  {
    metric: 'total_impressions',
    label: 'Impressions',
    description: 'Total number of ad impressions',
    format: 'number',
    color: '#14b8a6',
    weight: 0
  },
  {
    metric: 'total_reach',
    label: 'Reach',
    description: 'Total unique people reached',
    format: 'number',
    color: '#f97316',
    weight: 0
  },
  {
    metric: 'avg_frequency',
    label: 'Frequency',
    description: 'Average number of times each person saw the ad',
    format: 'number',
    color: '#a855f7',
    weight: 0
  },
  {
    metric: 'total_purchases',
    label: 'Purchases',
    description: 'Total number of purchases',
    format: 'number',
    color: '#22c55e',
    weight: 0
  },
  {
    metric: 'total_purchase_value',
    label: 'Purchase Value',
    description: 'Total value of purchases',
    format: 'currency',
    color: '#eab308',
    weight: 0
  },
  {
    metric: 'total_landing_page_views',
    label: 'Landing Page Views',
    description: 'Total landing page views',
    format: 'number',
    color: '#06b6d4',
    weight: 0
  },
  {
    metric: 'cost_per_landing_page_view',
    label: 'Cost per Landing Page View',
    description: 'Cost per landing page view',
    format: 'currency',
    color: '#dc2626',
    weight: 0
  },
  {
    metric: 'total_outbound_clicks',
    label: 'Outbound Clicks',
    description: 'Total clicks to external website',
    format: 'number',
    color: '#7c3aed',
    weight: 0
  },
  {
    metric: 'total_video_plays_3s',
    label: '3-Second Video Plays',
    description: 'Videos played for at least 3 seconds',
    format: 'number',
    color: '#059669',
    weight: 0
  },
  {
    metric: 'total_add_to_carts',
    label: 'Add to Carts',
    description: 'Total add to cart actions',
    format: 'number',
    color: '#0891b2',
    weight: 0
  },
  {
    metric: 'cost_per_add_to_cart',
    label: 'Cost per Add to Cart',
    description: 'Cost per add to cart action',
    format: 'currency',
    color: '#be185d',
    weight: 0
  },
  {
    metric: 'average_order_value',
    label: 'Average Order Value',
    description: 'Average value per purchase',
    format: 'currency',
    color: '#7c2d12',
    weight: 0
  },
  {
    metric: 'unique_outbound_clicks',
    label: 'Unique Outbound Clicks',
    description: 'Unique clicks to external website',
    format: 'number',
    color: '#1e40af',
    weight: 0
  }
];

// Legacy constant for backward compatibility
export const AD_WINNER_METRICS: MetricOption[] = [
  {
    label: 'ROAS',
    value: 'roas',
    description: 'Return on Ad Spend',
    format: 'number',
  },
  {
    label: 'Conversion Rate',
    value: 'conversion_rate',
    description: 'Purchase rate (purchases / unique outbound clicks)',
    format: 'percentage',
  },
  {
    label: 'Hook Rate',
    value: 'hook_rate',
    description: 'Video view rate (3-second plays / impressions)',
    format: 'percentage',
  },
  {
    label: 'Hold Rate',
    value: 'hold_rate',
    description:
      'Video retention rate (viewers who watched 100% / 3-second plays)',
    format: 'percentage',
  },
  {
    label: 'Traffic Quality',
    value: 'traffic_quality',
    description:
      'Landing page view rate (landing page views / outbound clicks)',
    format: 'percentage',
  },
  {
    label: 'CPA',
    value: 'cpa',
    description: 'Cost per Acquisition',
    format: 'currency',
  },
  {
    label: 'CPM',
    value: 'avg_cpm',
    description: 'Cost per Mille (thousand impressions)',
    format: 'currency',
  },
  {
    label: 'CTR',
    value: 'outbound_ctr',
    description: 'Click-through Rate',
    format: 'percentage',
  },
];

export const AD_WINNER_TABLE_COLUMNS: AdWinnerTableColumn[] = [
  {
    field: 'creative.image_url',
    header: 'Creative',
    sortable: false,
    filterable: false,
  },
  { field: 'name', header: 'Ad Name', sortable: true, filterable: true },
  { field: 'adset.name', header: 'Ad Set', sortable: true, filterable: true },
  {
    field: 'campaign.name',
    header: 'Campaign',
    sortable: true,
    filterable: true,
  },
  {
    field: 'performance_score',
    header: 'Score',
    sortable: true,
    filterable: false,
    format: 'number',
  },
  {
    field: 'weekly_insights.roas',
    header: 'ROAS',
    sortable: true,
    filterable: false,
    format: 'number',
  },
  {
    field: 'weekly_insights.total_spend',
    header: 'Spend',
    sortable: true,
    filterable: false,
    format: 'currency',
  },
  {
    field: 'weekly_insights.conversion_rate',
    header: 'Conv. Rate',
    sortable: true,
    filterable: false,
    format: 'percentage',
  },
  {
    field: 'weekly_insights.hook_rate',
    header: 'Hook Rate',
    sortable: true,
    filterable: false,
    format: 'percentage',
  },
  {
    field: 'weekly_insights.hold_rate',
    header: 'Hold Rate',
    sortable: true,
    filterable: false,
    format: 'percentage',
  },
  {
    field: 'weekly_insights.unique_outbound_clicks',
    header: 'Unique Clicks',
    sortable: true,
    filterable: false,
    format: 'number',
  },
  {
    field: 'rank_in_adset',
    header: 'Rank',
    sortable: true,
    filterable: false,
    format: 'number',
  },
  {
    field: 'runningTimeDays',
    header: 'Running Time',
    sortable: true,
    filterable: false,
    format: 'number',
  },
];
