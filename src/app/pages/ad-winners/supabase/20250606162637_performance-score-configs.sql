-- Create performance_score_configurations table
CREATE TABLE performance_score_configurations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE, -- System templates cannot be deleted
    metrics_config JSONB NOT NULL, -- Flexible metric configuration
    created_by TEXT, -- User who created this config (for future user management)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique names per user (for future multi-user support)
    CONSTRAINT unique_config_name_per_user UNIQUE (name, created_by)
);

-- Create index for better performance
CREATE INDEX idx_performance_score_configs_name ON performance_score_configurations(name);
CREATE INDEX idx_performance_score_configs_is_default ON performance_score_configurations(is_default);
CREATE INDEX idx_performance_score_configs_is_system ON performance_score_configurations(is_system);
CREATE INDEX idx_performance_score_configs_created_by ON performance_score_configurations(created_by);

-- Add trigger for updated_at
CREATE TRIGGER update_performance_score_configurations_updated_at
    BEFORE UPDATE ON performance_score_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE performance_score_configurations ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON performance_score_configurations
    FOR ALL USING (auth.role() = 'authenticated');

-- Insert default system configurations
INSERT INTO performance_score_configurations (name, description, is_default, is_system, metrics_config, created_by) VALUES
(
    'E-commerce Focused',
    'Optimized for online sales with emphasis on ROAS and conversion rate',
    TRUE,
    TRUE,
    '{
        "metrics": [
            {"metric": "roas", "label": "ROAS", "description": "Return on Ad Spend", "format": "number", "color": "#10b981", "weight": 35},
            {"metric": "conversion_rate", "label": "Conversion Rate", "description": "Purchase rate (purchases / unique outbound clicks)", "format": "percentage", "color": "#3b82f6", "weight": 30},
            {"metric": "traffic_quality", "label": "Traffic Quality", "description": "Landing page view rate (landing page views / outbound clicks)", "format": "percentage", "color": "#8b5cf6", "weight": 15},
            {"metric": "cpa", "label": "CPA", "description": "Cost per acquisition", "format": "currency", "color": "#ef4444", "weight": 10},
            {"metric": "hook_rate", "label": "Hook Rate", "description": "Video view rate (3-second plays / impressions)", "format": "percentage", "color": "#f59e0b", "weight": 5},
            {"metric": "hold_rate", "label": "Hold Rate", "description": "Video retention rate (viewers who watched 100% / 3-second plays)", "format": "percentage", "color": "#06b6d4", "weight": 5},
            {"metric": "avg_cpm", "label": "CPM", "description": "Cost per thousand impressions", "format": "currency", "color": "#84cc16", "weight": 0},
            {"metric": "outbound_ctr", "label": "Outbound CTR", "description": "Click-through rate to landing page", "format": "percentage", "color": "#ec4899", "weight": 0}
        ]
    }'::jsonb,
    'system'
),
(
    'Brand Awareness',
    'Focus on reach and engagement for brand building campaigns',
    FALSE,
    TRUE,
    '{
        "metrics": [
            {"metric": "hook_rate", "label": "Hook Rate", "description": "Video view rate (3-second plays / impressions)", "format": "percentage", "color": "#f59e0b", "weight": 30},
            {"metric": "hold_rate", "label": "Hold Rate", "description": "Video retention rate (viewers who watched 100% / 3-second plays)", "format": "percentage", "color": "#06b6d4", "weight": 25},
            {"metric": "avg_cpm", "label": "CPM", "description": "Cost per thousand impressions", "format": "currency", "color": "#84cc16", "weight": 20},
            {"metric": "traffic_quality", "label": "Traffic Quality", "description": "Landing page view rate (landing page views / outbound clicks)", "format": "percentage", "color": "#8b5cf6", "weight": 15},
            {"metric": "roas", "label": "ROAS", "description": "Return on Ad Spend", "format": "number", "color": "#10b981", "weight": 10},
            {"metric": "conversion_rate", "label": "Conversion Rate", "description": "Purchase rate (purchases / unique outbound clicks)", "format": "percentage", "color": "#3b82f6", "weight": 0},
            {"metric": "cpa", "label": "CPA", "description": "Cost per acquisition", "format": "currency", "color": "#ef4444", "weight": 0},
            {"metric": "outbound_ctr", "label": "Outbound CTR", "description": "Click-through rate to landing page", "format": "percentage", "color": "#ec4899", "weight": 0}
        ]
    }'::jsonb,
    'system'
),
(
    'Balanced Performance',
    'Equal weight across all key performance indicators',
    FALSE,
    TRUE,
    '{
        "metrics": [
            {"metric": "roas", "label": "ROAS", "description": "Return on Ad Spend", "format": "number", "color": "#10b981", "weight": 15},
            {"metric": "conversion_rate", "label": "Conversion Rate", "description": "Purchase rate (purchases / unique outbound clicks)", "format": "percentage", "color": "#3b82f6", "weight": 15},
            {"metric": "hook_rate", "label": "Hook Rate", "description": "Video view rate (3-second plays / impressions)", "format": "percentage", "color": "#f59e0b", "weight": 15},
            {"metric": "hold_rate", "label": "Hold Rate", "description": "Video retention rate (viewers who watched 100% / 3-second plays)", "format": "percentage", "color": "#06b6d4", "weight": 15},
            {"metric": "traffic_quality", "label": "Traffic Quality", "description": "Landing page view rate (landing page views / outbound clicks)", "format": "percentage", "color": "#8b5cf6", "weight": 15},
            {"metric": "cpa", "label": "CPA", "description": "Cost per acquisition", "format": "currency", "color": "#ef4444", "weight": 10},
            {"metric": "avg_cpm", "label": "CPM", "description": "Cost per thousand impressions", "format": "currency", "color": "#84cc16", "weight": 10},
            {"metric": "outbound_ctr", "label": "Outbound CTR", "description": "Click-through rate to landing page", "format": "percentage", "color": "#ec4899", "weight": 5}
        ]
    }'::jsonb,
    'system'
);

-- Add comment for documentation
COMMENT ON TABLE performance_score_configurations IS 'User-defined performance score calculation configurations with flexible metric weights';
COMMENT ON COLUMN performance_score_configurations.metrics_config IS 'JSONB containing metric definitions with weights, labels, colors, and formats';
COMMENT ON COLUMN performance_score_configurations.is_system IS 'System templates cannot be deleted by users';
COMMENT ON COLUMN performance_score_configurations.is_default IS 'Default configuration to use when none is selected';
