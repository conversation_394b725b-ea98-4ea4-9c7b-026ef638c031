-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create accounts table
CREATE TABLE accounts (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        status TEXT,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create campaigns table
CREATE TABLE campaigns (
                         id TEXT PRIMARY KEY,
                         account_id TEXT NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
                         name TEXT NOT NULL,
                         status TEXT,
                         objective TEXT,
                         created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                         updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create adsets table
CREATE TABLE adsets (
                      id TEXT PRIMARY KEY,
                      campaign_id TEXT NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
                      account_id TEXT NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
                      name TEXT NOT NULL,
                      status TEXT,
                      targeting <PERSON><PERSON>N<PERSON>,
                      budget_remaining DECIMAL(15,2),
                      daily_budget DECIMAL(15,2),
                      lifetime_budget DECIMAL(15,2),
                      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create videos table
CREATE TABLE videos (
                      id TEXT PRIMARY KEY,
                      source TEXT,
                      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ads table (за бъдещо разширение)
CREATE TABLE ads (
                   id TEXT PRIMARY KEY,
                   adset_id TEXT NOT NULL REFERENCES adsets(id) ON DELETE CASCADE,
                   campaign_id TEXT NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
                   account_id TEXT NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
                   name TEXT NOT NULL,
                   status TEXT,
                   creative JSONB,
                   video_id TEXT REFERENCES videos(id) ON DELETE SET NULL,
                   created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                   updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ad_insights_weekly table for weekly ad performance metrics
CREATE TABLE ad_insights_weekly (
                                  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                                  ad_id TEXT NOT NULL REFERENCES ads(id) ON DELETE CASCADE,
                                  adset_id TEXT NOT NULL REFERENCES adsets(id) ON DELETE CASCADE,
                                  campaign_id TEXT NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
                                  account_id TEXT NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
                                  week_start DATE NOT NULL,
                                  week_end DATE NOT NULL,

  -- Basic metrics
                                  total_impressions INTEGER DEFAULT 0,
                                  total_spend DECIMAL(15,2) DEFAULT 0,
                                  total_reach INTEGER DEFAULT 0,
                                  avg_cpm DECIMAL(10,4) DEFAULT 0,
                                  avg_frequency DECIMAL(10,4) DEFAULT 0,

  -- Conversion metrics
                                  total_purchases INTEGER DEFAULT 0,
                                  total_purchase_value DECIMAL(15,2) DEFAULT 0,
                                  cpa DECIMAL(10,4),
                                  roas DECIMAL(10,4),

  -- Engagement metrics
                                  total_landing_page_views INTEGER DEFAULT 0,
                                  cost_per_landing_page_view DECIMAL(10,4),
                                  total_outbound_clicks INTEGER DEFAULT 0,
                                  outbound_ctr DECIMAL(10,4),
                                  total_video_plays_3s INTEGER DEFAULT 0,
                                  total_add_to_carts INTEGER DEFAULT 0,
                                  cost_per_add_to_cart DECIMAL(10,4),

  -- Advanced metrics
                                  hook_rate DECIMAL(10,6),
                                  hold_rate DECIMAL(10,6),
                                  conversion_rate DECIMAL(10,6),
                                  average_order_value DECIMAL(10,4),
                                  traffic_quality DECIMAL(10,6),

                                  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                                  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

                                  UNIQUE(ad_id, week_start, week_end)
);



-- Create indexes for better performance
CREATE INDEX idx_campaigns_account_id ON campaigns(account_id);
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_campaigns_objective ON campaigns(objective);

CREATE INDEX idx_adsets_campaign_id ON adsets(campaign_id);
CREATE INDEX idx_adsets_account_id ON adsets(account_id);
CREATE INDEX idx_adsets_status ON adsets(status);

CREATE INDEX idx_ads_adset_id ON ads(adset_id);
CREATE INDEX idx_ads_campaign_id ON ads(campaign_id);
CREATE INDEX idx_ads_account_id ON ads(account_id);
CREATE INDEX idx_ads_video_id ON ads(video_id);

CREATE INDEX idx_ad_insights_weekly_ad_id ON ad_insights_weekly(ad_id);
CREATE INDEX idx_ad_insights_weekly_week_start ON ad_insights_weekly(week_start);
CREATE INDEX idx_ad_insights_weekly_account_id ON ad_insights_weekly(account_id);
CREATE INDEX idx_ad_insights_weekly_campaign_id ON ad_insights_weekly(campaign_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_adsets_updated_at BEFORE UPDATE ON adsets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ads_updated_at BEFORE UPDATE ON ads
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ad_insights_weekly_updated_at BEFORE UPDATE ON ad_insights_weekly
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


-- Enable Row Level Security (RLS)
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE adsets ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_insights_weekly ENABLE ROW LEVEL SECURITY;

-- Create policies (adjust based on your auth requirements)
CREATE POLICY "Enable all operations for authenticated users" ON accounts
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON campaigns
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON adsets
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON videos
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON ads
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON ad_insights_weekly
    FOR ALL USING (auth.role() = 'authenticated');

-- Add comments for documentation
COMMENT ON TABLE accounts IS 'Facebook Ad Accounts';
COMMENT ON TABLE campaigns IS 'Facebook Ad Campaigns';
COMMENT ON TABLE adsets IS 'Facebook Ad Sets';
COMMENT ON TABLE videos IS 'Video assets used in ads';
COMMENT ON TABLE ads IS 'Facebook Ads';
COMMENT ON TABLE ad_insights_weekly IS 'Weekly aggregated ad performance insights with comprehensive metrics';

COMMENT ON COLUMN ad_insights_weekly.total_spend IS 'Total spend in the currency of the account';
COMMENT ON COLUMN ad_insights_weekly.avg_cpm IS 'Average cost per mille (thousand impressions)';
COMMENT ON COLUMN ad_insights_weekly.avg_frequency IS 'Average frequency of ad delivery';
COMMENT ON COLUMN ad_insights_weekly.roas IS 'Return on ad spend';
COMMENT ON COLUMN ad_insights_weekly.hook_rate IS 'Video view rate (3-second plays / impressions)';
COMMENT ON COLUMN ad_insights_weekly.conversion_rate IS 'Purchase rate (purchases / landing page views)';
COMMENT ON COLUMN ad_insights_weekly.traffic_quality IS 'Landing page view rate (landing page views / outbound clicks)';

CREATE VIEW view_ad_insights_weekly_periods AS
SELECT DISTINCT
  week_start,
  week_end
FROM ad_insights_weekly
ORDER BY week_start DESC;

-- Add unique_outbound_clicks column to ad_insights_weekly table
ALTER TABLE ad_insights_weekly ADD COLUMN unique_outbound_clicks INTEGER DEFAULT 0;

-- Add created_time column to ad_insights_weekly table
ALTER TABLE ad_insights_weekly ADD COLUMN created_time TIMESTAMP WITH TIME ZONE DEFAULT '2025-06-02T00:43:17+0300';
